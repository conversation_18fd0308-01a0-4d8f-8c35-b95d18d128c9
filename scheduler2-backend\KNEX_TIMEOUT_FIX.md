# Knex Connection Pool Timeout Fix

## Problem
The application was experiencing `KnexTimeoutError: Knex: Timeout acquiring a connection. The pool is probably full. Are you missing a .transacting(trx) call?` errors, particularly with the `getUserRoles` query that joins user_roles, departments, roles, and states tables.

## Root Causes Identified
1. **Inadequate Pool Configuration**: Pool had `min: 0` connections and only `max: 25`, causing connection creation delays
2. **Frequent Authentication Calls**: `getUserRoles` was called during every authentication/session deserialization without caching
3. **No Connection Cleanup**: Some queries might not have been properly releasing connections
4. **Missing Error Handling**: Queries could hang indefinitely without timeouts
5. **No Monitoring**: No visibility into connection pool health or query performance

## Fixes Implemented

### 1. Enhanced Database Connection Pool Configuration
**File**: `scheduler2-backend/src/config/databaseConnect.js`

- Increased `max` connections from 25 to 50
- Set `min` connections to 2 (keep connections alive)
- Increased `acquireTimeoutMillis` from 30s to 60s
- Added `createTimeoutMillis`, `destroyTimeoutMillis`, and other pool settings
- Added connection validation and debug mode for development

### 2. Query Optimization and Timeout Protection
**File**: `scheduler2-backend/src/models/usersModel.js`

- Added 15-second timeout to `getUserRoles` query using `Promise.race`
- Added 10-second timeout to `getUserLoginInfo` query
- Enhanced error handling with specific timeout messages
- Added input validation (OID required checks)

### 3. Caching Implementation
**File**: `scheduler2-backend/src/models/usersModel.js`

- Implemented in-memory cache for user roles with 5-minute TTL
- Cache automatically clears when user roles are modified
- Reduces database load for frequently accessed user data
- Automatic cache cleanup to prevent memory leaks

### 4. Connection Monitoring and Health Checks
**Files**: 
- `scheduler2-backend/src/utils/dbMonitor.js` (new)
- `scheduler2-backend/src/api/resolvers/health/dbHealth.js` (new)
- `scheduler2-backend/src/api/routes/health.js` (updated)

- Real-time query monitoring and statistics
- Slow query detection (>2 seconds)
- Connection pool health monitoring
- Database health check endpoint at `/health/db`
- Automatic logging of database statistics

### 5. Enhanced Error Handling and Logging
**File**: `scheduler2-backend/src/config/databaseConnect.js`

- Added query and error event listeners
- Connection pool monitoring with periodic health checks
- Graceful shutdown handling for SIGINT/SIGTERM
- Enhanced logging for debugging

## Testing the Fixes

### 1. Check Database Health
```bash
curl http://localhost:3000/health/db
```

### 2. Monitor Logs
Look for these log messages:
- "Database monitoring started"
- "Database health check passed"
- "Connection Pool Stats" (in development)
- "Returning cached user roles" (when cache is working)

### 3. Performance Monitoring
- Slow queries (>2s) will be logged as warnings
- Database stats logged every 5 minutes
- Pool health checked every 2 minutes

## Configuration Changes Summary

### Before:
```javascript
pool: {
  min: 0,
  max: 25,
  acquireTimeoutMillis: 30000,
  idleTimeoutMillis: 90000,
}
```

### After:
```javascript
pool: {
  min: 2,
  max: 50,
  acquireTimeoutMillis: 60000,
  createTimeoutMillis: 30000,
  destroyTimeoutMillis: 5000,
  idleTimeoutMillis: 300000,
  reapIntervalMillis: 1000,
  createRetryIntervalMillis: 200,
  propagateCreateError: false,
}
```

## Expected Results
1. **Eliminated Timeout Errors**: Connection pool should handle concurrent requests better
2. **Improved Performance**: Caching reduces database load for user role queries
3. **Better Monitoring**: Real-time visibility into database performance
4. **Graceful Error Handling**: Queries timeout gracefully instead of hanging
5. **Proactive Issue Detection**: Slow queries and connection issues are logged

## Maintenance
- Monitor the `/health/db` endpoint regularly
- Check logs for slow query warnings
- Cache will automatically manage itself but can be cleared if needed
- Pool statistics help identify if further tuning is needed

## Rollback Plan
If issues occur, you can quickly revert the pool configuration in `databaseConnect.js` to the original settings, but the monitoring and caching improvements should be kept as they provide valuable insights and performance benefits.
