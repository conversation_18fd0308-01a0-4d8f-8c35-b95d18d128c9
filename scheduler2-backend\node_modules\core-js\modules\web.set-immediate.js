'use strict';
var $ = require('../internals/export');
var globalThis = require('../internals/global-this');
var setTask = require('../internals/task').set;
var schedulersFix = require('../internals/schedulers-fix');

// https://github.com/oven-sh/bun/issues/1633
var setImmediate = globalThis.setImmediate ? schedulersFix(setTask, false) : setTask;

// `setImmediate` method
// http://w3c.github.io/setImmediate/#si-setImmediate
$({ global: true, bind: true, enumerable: true, forced: globalThis.setImmediate !== setImmediate }, {
  setImmediate: setImmediate
});
