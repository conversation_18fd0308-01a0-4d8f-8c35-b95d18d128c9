{"name": "scheduler2-backend", "version": "1.0.0", "description": "backend for scheduler 2.0", "main": "bin/www", "scripts": {"test": "env-cmd -f .dev.env jest --verbose --runInBand --detectOpenHandles --forceExit --silent", "lint": "eslint src", "dev": "env-cmd -f .dev.env nodemon --exec babel-node ./src/app.js", "build": "webpack", "prettier": "prettier --write \"**/*.js\""}, "repository": {"type": "git", "url": "git+https://github.com/HomeWorksEnergy/scheduler2-backend.git"}, "author": "<EMAIL>", "license": "ISC", "bugs": {"url": "https://github.com/HomeWorksEnergy/scheduler2-backend/issues"}, "homepage": "https://github.com/HomeWorksEnergy/scheduler2-backend#readme", "dependencies": {"@aws-sdk/client-kms": "^3.840.0", "@aws-sdk/client-location": "^3.840.0", "@aws-sdk/client-s3": "^3.842.0", "@aws-sdk/s3-request-presigner": "^3.842.0", "@homeworksenergy/utility-service": "^1.2.1", "axios": "^1.10.0", "bcrypt": "^5.0.1", "body-parser": "^1.19.0", "connect-redis": "^9.0.0", "cookie-parser": "^1.4.5", "core-js": "^3.8.3", "cors": "^2.8.5", "cross-blob": "^3.0.2", "csvtojson": "^2.0.10", "ejs": "^3.1.10", "express": "^4.17.1", "express-formidable": "^1.2.0", "express-session": "^1.17.1", "jsforce": "^3.9.1", "knex": "^3.1.0", "limiter": "^1.1.4", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.32", "morgan": "^1.10.0", "passport": "^0.7.0", "passport-azure-ad": "^4.3.5", "pdf-lib": "^1.16.0", "pg": "^8.16.3", "pg-listen": "^1.7.0", "redis": "^5.7.0", "regenerator-runtime": "^0.13.7", "request": "^2.88.2", "request-promise-native": "^1.0.5", "winston": "^3.3.3"}, "devDependencies": {"@babel/cli": "^7.12.10", "@babel/core": "^7.12.10", "@babel/node": "^7.12.10", "@babel/preset-env": "^7.12.11", "axios-mock-adapter": "^1.20.0", "babel-eslint": "^10.1.0", "babel-jest": "^30.0.4", "babel-loader": "^8.2.2", "babel-plugin-module-resolver": "^4.1.0", "babel-preset-minify": "^0.5.1", "chai": "^4.2.0", "chai-http": "^4.3.0", "clean-webpack-plugin": "^4.0.0", "env-cmd": "^10.1.0", "eslint": "^6.8.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.3.1", "file-loader": "^5.1.0", "jest": "^30.0.4", "nodemon": "^3.1.10", "prettier": "^1.19.1", "supertest": "^5.0.0", "terser-webpack-plugin": "^5.3.0", "webpack": "^5.65.0", "webpack-cli": "^4.9.1", "webpack-node-externals": "^3.0.0"}, "jest": {"testEnvironment": "node", "setupFilesAfterEnv": ["./src/tests/setupTests.js"]}}