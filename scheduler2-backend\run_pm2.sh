echo "START pm2 file execution"
export HOME="/home/<USER>"
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"

echo "START cd /home/<USER>/scheduler2-backend"
cd /home/<USER>/scheduler2-backend
echo "START cp -t ./../build/ -a src/views/."
cp -t ./../build/ -a src/views/.

echo "START cd /home/<USER>/build"
cd /home/<USER>/build
echo "START npm install --omit=dev"
npm install --omit=dev
echo "START pm2 restart all"
pm2 restart all
echo "FINISH pm2 file execution.."

# Below is script if needed to run for the very first time, pm2 remembers previous scripts so restarting should be enough
# pm2 start build/index.js --name scheduler2 --output logs/out.log --error logs/err.log --time

