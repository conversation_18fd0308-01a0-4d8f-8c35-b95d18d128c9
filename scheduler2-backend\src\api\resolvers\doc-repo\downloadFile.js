const { Blob } = require('buffer'); // Ensure cross-blob is installed for Blob support in Node.js
const docRepoModel = require('@models/docRepoModel');
const eventsModel = require('@models/eventsModel');
const config = require('@config/config');

const downloadFile = async (req, res, next) => {
  global.logger.debug('resolvers/doc-repo/downloadFile');
  global.logger.debug('with params: \n%o', { user: req.user, ...req.query });
  try {
    const operatorId = req.user._json.oid;
    const { doc, state, department, uniqueId } = req.query;
    let s3Bucket = config.s3DocRepoPath;
    let s3Key = `${state}/${department.replace('-', '/')}/${uniqueId}/${doc}`;
    if (department === 'Partners' && ['PICS.pdf', 'PWB.pdf'].includes(doc)) {
      s3Bucket = config.sch1DocRepoPath;
      s3Key = `${doc}/${uniqueId.slice(0, 15)}`;
    }
    const s3BucketParams = {
      Bucket: s3Bucket,
      Key: s3Key,
    };
    let response = await docRepoModel.downloadFile(s3BucketParams);
    let fileExists = true;
    if (response?.statusCode === 404 && department === 'HVAC_Install') {
      const bucketParams = [];
      const searchResults = await eventsModel.searchEvents({
        searchTerm: uniqueId,
        departmentEventTypes: ['0001'],
        fieldsToSearch: ['type'],
      });
      if (searchResults.length > 0) {
        const [{ sfIds }] = searchResults;
        bucketParams.push({
          Bucket: config.s3DocRepoPath,
          Key: `${state}/HVAC_Sales/${sfIds.opportunityId}/${doc}`,
        });
      }
      for (let i = 0; i < bucketParams.length; i++) {
        fileExists = await docRepoModel.doesFileExist(bucketParams[i]);
        if (fileExists) {
          response = await docRepoModel.downloadFile(bucketParams[i]);
          break;
        }
      }
    }

    const fileBytes = await response.Body.transformToByteArray();
    const file = new Blob([fileBytes], { type: response.ContentType });

    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    const reportObj = {
      oid: operatorId,
      action: 'download',
      docName: doc,
      sfId: uniqueId,
      state,
      department,
    };
    await docRepoModel.docRepoReport(reportObj);

    return res.send(buffer);
  } catch (error) {
    return next(error);
  }
};

module.exports = downloadFile;
