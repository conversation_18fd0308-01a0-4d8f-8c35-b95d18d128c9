const docRepoModel = require('@models/docRepoModel');
const eventsModel = require('@models/eventsModel');
const config = require('@config/config');
const getSignedUrl = async (req, res, next) => {
  global.logger.debug('resolvers/doc-repo/uploadFile');
  global.logger.debug('with params: \n%o', {
    user: req.user,
    ...req.params,
  });

  const { docName, state, uniqueId } = req.params;
  let { department } = req.params;
  department = department.replace('-', '/');
  try {
    let fileExists = false;
    let signedUrl = '';
    const s3ObjectParams = {
      Bucket: config.s3DocRepoPath,
      Key: `${state}/${department}/${uniqueId}/${docName}`,
    };
    if (department === 'Partners' && ['PICS.pdf', 'PWB.pdf', 'MOLD.pdf'].includes(docName)) {
      s3ObjectParams.Bucket = config.sch1DocRepoPath;
      s3ObjectParams.Key = `${uniqueId.slice(0, 15)}/${s3ObjectParams.Key}`;
      fileExists = await docRepoModel.doesFileExist(s3ObjectParams);
      if (!fileExists) {
        s3ObjectParams.Bucket = config.s3DocRepoPath;
        s3ObjectParams.Key = `${state}/HEA/CAP/${uniqueId}/${docName}`;
      }
      fileExists = await docRepoModel.doesFileExist(s3ObjectParams);
    } else {
      fileExists = await docRepoModel.doesFileExist(s3ObjectParams);
      if (!fileExists && department === 'HVAC_Install') {
        const bucketParams = [];
        const searchResults = await eventsModel.searchEvents({
          searchTerm: uniqueId,
          departmentEventTypes: ['0001'],
          fieldsToSearch: ['type'],
        });
        if (searchResults.length > 0) {
          const [{ sfIds }] = searchResults;
          bucketParams.push({
            Bucket: config.s3DocRepoPath,
            Key: `${state}/HVAC_Sales/${sfIds.opportunityId}/${docName}`,
          });
        }
        for (let i = 0; i < bucketParams.length; i++) {
          fileExists = await docRepoModel.doesFileExist(bucketParams[i]);
          if (fileExists) {
            signedUrl = await docRepoModel.getDocumentUrl(bucketParams[i]);
            break;
          }
        }
      }
    }
    if (!fileExists) {
      return res.json({ message: 'S3 Bucket does not contain such file' });
    }
    if (signedUrl.length === 0) signedUrl = await docRepoModel.getDocumentUrl(s3ObjectParams);
    return res.json({ signedUrl });
  } catch (error) {
    return next(error);
  }
};

module.exports = getSignedUrl;
