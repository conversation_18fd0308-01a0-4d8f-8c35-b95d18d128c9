const dbMonitor = require('@utils/dbMonitor');

const dbHealth = async (req, res, next) => {
  try {
    const healthCheck = await dbMonitor.checkHealth();
    const poolInfo = dbMonitor.getPoolInfo();
    
    const response = {
      database: {
        healthy: healthCheck.healthy,
        responseTime: healthCheck.responseTime,
        error: healthCheck.error
      },
      connectionPool: poolInfo,
      timestamp: new Date().toISOString()
    };

    if (healthCheck.healthy) {
      return res.status(200).json(response);
    } else {
      return res.status(503).json(response);
    }
  } catch (error) {
    global.logger?.error('Database health check endpoint failed: %o', error);
    return next(error);
  }
};

module.exports = dbHealth;
