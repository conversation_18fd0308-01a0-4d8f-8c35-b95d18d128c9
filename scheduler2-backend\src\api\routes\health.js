const express = require('express');
const router = express.Router();
const dbHealth = require('../resolvers/health/dbHealth');

router.get('/', (req, res) => {
  res.status(200).json({
    message: 'normal',
  });
  res.end('');
});

router.get('/db', dbHealth);
// router.get('/loginstatus', ensureAuthenticated, function(req, res) {
//   res.status(200).json({
//     message: 'normal'
//   });
//   res.end("");
// });

module.exports = router;

// const checkPermissions = (req, res, next) => {
//   if (req.isAuthenticated()) {
//     return next();
//   }
//   res.status(400).json({
//     success: false,
//     message: 'access denied',
//   });
// };
