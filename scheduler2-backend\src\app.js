require('core-js/stable');
require('regenerator-runtime/runtime');

const path = require('path');
const Express = require('express');
const session = require('express-session');
const bodyParser = require('body-parser');
const cookieParser = require('cookie-parser');
const passport = require('passport');
const { OIDCStrategy } = require('passport-azure-ad');
const newRedis = require('redis');
const morgan = require('morgan');
const { RedisStore } = require('connect-redis');
const logger = require('./config/logger');
const addDevUser = require('./api/middleware/addDevUser');
const config = require('@config/config');
const databaseListen = require('@config/databaseListen');
const { isHWEUser } = require('@homeworksenergy/utility-service');

const { auth, health, user } = require('./api/routes');
const routes = require('./config/routes');
const usersModel = require('./models/usersModel');
const axios = require('axios');
const cors = require('cors');

// require('@src/views/error.ejs');
// require('@src/views/login.ejs');
// require('@src/views/react_index.ejs');

/* App Init */
const app = new Express();

global.logger = logger;

global.logger.info('ENV VARIABLES: %o', config);
global.logger.info('process.env.NODE_ENV : %o', process.env.NODE_ENV);

// Temporarily disable database listener to prevent crashes
// TODO: Re-enable when database connection is stable
// databaseListen.connect().catch((error) => {
//   global.logger.error('Failed to initialize database listener: %o', error.message);
// });
global.logger.info('Database listener disabled to prevent connection issues');

const morganSkip = (req) => {
  const conditions = [req.url === '/health', req.url.startsWith('/utility')];
  return conditions.some((condition) => condition);
};

if (process.env.NODE_ENV === 'development') {
  app.use(
    morgan('dev', {
      skip: morganSkip,
      stream: logger.stream,
    })
  );
} else if (process.env.NODE_ENV === 'production') {
  app.use(
    morgan('common', {
      skip: morganSkip,
      stream: logger.stream,
    })
  );
}

app.use(
  bodyParser.urlencoded({
    extended: true,
  })
);
app.use(bodyParser.json());
app.use(cookieParser());

app.set('trust proxy', true);

// Create session object
const sessionObj = {
  secret: config.server.sessionSecret,
  saveUninitialized: false,
  resave: false,
  cookie: {
    maxAge: 3600000 * 10, // 10 hours
    domain: config.server.domain,
    sameSite: 'none',
    secure: true,
  },
  name: 'sessionid',
  rolling: true,
};

/**
 * Only set up azure passport authentication and redis client on production
 */
// **********************************************************************UNCOMMENT THIS FOR PRODUCTION**********************************************************************
if (process.env.NODE_ENV === 'production') {
  // Passport Auth
  passport.serializeUser((userObj, done) => {
    done(null, userObj);
  });

  passport.deserializeUser(async (userObj, done) => {
    try {
      // getting logged in users roles with department from roles_users table to set to a cookie
      const roles = await usersModel.getUserRoles(userObj._json.oid);
      const { company, isManager, department, state } = await usersModel.getUserLoginInfo(
        userObj._json.oid
      );
      userObj = {
        ...userObj,
        roles,
        company,
        isManager,
        department,
        state,
        hweUser: isHWEUser({ company }),
      };
      return done(null, userObj);
    } catch (error) {
      global.logger.info('caught error: %o', error);
      done(error);
    }
  });

  const {
    redirectUrl,
    allowHttpForRedirectUrl,
    realm,
    clientID,
    clientSecret,
    oidcIssuer,
    identityMetadata,
    skipUserProfile,
    responseType,
    responseMode,
  } = config.azure;

  passport.use(
    new OIDCStrategy(
      {
        redirectUrl,
        allowHttpForRedirectUrl,
        realm,
        clientID,
        clientSecret,
        oidcIssuer,
        identityMetadata,
        skipUserProfile,
        responseType,
        responseMode,
        validateIssuer: false,
        loggingLevel: 'info',
        loggingNoPII: false,
      },
      (iss, sub, profile, accessToken, refreshToken, done) => {
        if (!profile.oid) {
          return done(new Error('No oid found'), null);
        }
        return done(null, profile);
      }
    )
  );

  // Redis - Setup with proper error handling
  try {
    const redisClient = newRedis.createClient({
      url: `redis://staging-re-ro.s3dl5w.ng.0001.use1.cache.amazonaws.com:6379`,
      // Add connection timeout and retry settings
      socket: {
        connectTimeout: 30000, // 30 seconds
        lazyConnect: true,
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            global.logger?.error('Redis connection failed after 10 retries, falling back to memory store');
            return false; // Stop retrying
          }
          return Math.min(retries * 100, 3000); // Exponential backoff, max 3 seconds
        }
      }
    });

    redisClient.on('error', (error) => {
      global.logger?.error('Redis connection error: %o', error.message);
    });

    redisClient.on('connect', () => {
      global.logger?.info('Redis client connected');
    });

    redisClient.on('ready', () => {
      global.logger?.info('Redis client ready');
    });

    // Connect to Redis and set up session store
    redisClient.connect().then(() => {
      sessionObj.store = new RedisStore({
        client: redisClient,
      });
      global.logger?.info('Redis session store configured');
    }).catch((error) => {
      global.logger?.error('Failed to connect to Redis, using memory store: %o', error.message);
      // Session will use default memory store if Redis fails
    });
  } catch (error) {
    global.logger?.error('Redis setup failed, using memory store: %o', error.message);
  }
}

// Initialize session and passport
// Add a warning suppression for memory store in production if Redis fails
if (process.env.NODE_ENV === 'production' && !sessionObj.store) {
  global.logger?.warn('Using memory store for sessions - Redis connection may have failed');
}

app.use(session(sessionObj));
app.use(passport.initialize());
app.use(passport.session());

/**
 * Bypass Azure login for development (will already have a req.user object)
 */
// **********************************************************************UNCOMMENT THIS FOR PRODUCTION**********************************************************************
if (process.env.NODE_ENV === 'development') {
  app.use(addDevUser);
}

// **********************************************************************REMOVE THIS FOR PRODUCTION**********************************************************************

app.use(cors());

app.use('/api/user/', user);
app.use('/auth/openid/', auth);
app.use('/health/', health);
app.use('/api', routes);

// views and resources:
app.set('view engine', 'ejs');

let viewPath = './src/views';
if (process.env.APP_ENV === 'production') viewPath = '/home/<USER>/hea/views';
if (process.env.APP_ENV === 'staging') viewPath = '/home/<USER>/scheduler2-backend/src/views';

app.set('views', viewPath);
// eslint-disable-next-line no-underscore-dangle
app.engine('ejs', require('ejs').__express);
app.use(Express.static(path.join(__dirname, '../public')));
if (process.env.NODE_ENV === 'development') {
  app.use(Express.static(path.join(__dirname, '../../scheduler2-frontend/dist')));
}

// CORS-- allow access to any homeworksenergy subdomain
app.use((req, res, next) => {
  res.header('Access-Control-Allow-Origin', '.homeworksenergy.com');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
  res.header('Cache-Control', 'no-cache,max-age=0');
  next();
});
// leave { next } param in this function
// eslint-disable-next-line
app.use((error, req, res, next) => {
  let jsonError = {};
  // Error object is not json, wont get sent back correctly
  // Use this to construct json object with info
  if (error.toJSON) {
    jsonError = error.toJSON();
  } else if (error instanceof Error) {
    Object.getOwnPropertyNames(error).forEach((key) => {
      jsonError[key] = error[key];
    });
  } else {
    jsonError = error;
  }
  logger.error('Caught error: \n%o', jsonError);
  return res.status(error.status || 400).send({ error: jsonError });
});

// leave { next } param in this function
// eslint-disable-next-line
app.get('*', async (req, res, next) => {
  if (!req.user) return res.redirect('/api/user/loginAzure');

  const { data } = await axios.get(`${config.bundleJsPath}/manifest.json`);
  const { error } = data;

  if (!error) {
    const [, fileName] = data['main.js'].split('/');
    const templateData = { bundleJsPath: `${config.bundleJsPath}/${fileName}` };
    return res.render('react_index', templateData);
  }

  return res.status(error.status || 400).send({ error });
});

const server = app.listen(config.server.port, () =>
  global.logger.info(`App is listening on port ${config.server.port}`)
);

module.exports = server;
