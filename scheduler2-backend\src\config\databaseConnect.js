const config = require('@config/config');
const { types } = require('pg');

// Change type parser to return floats instead of strings for numeric the database
types.setTypeParser(types.builtins.NUMERIC, (value) => {
  return parseFloat(value);
});

let knex = require('knex');

knex = knex({
  client: 'pg',
  connection: config.db.connectionString,
  searchPath: ['knex', 'public'],
  pool: {
    min: 2, // Keep minimum connections alive
    max: 50, // Increase max connections
    acquireTimeoutMillis: 60000, // Increase to 60 seconds
    createTimeoutMillis: 30000, // 30 seconds to create connection
    destroyTimeoutMillis: 5000, // 5 seconds to destroy connection
    idleTimeoutMillis: 300000, // 5 minutes idle timeout
    reapIntervalMillis: 1000, // Check for idle connections every second
    createRetryIntervalMillis: 200, // Retry connection creation every 200ms
    propagateCreateError: false, // Don't propagate connection creation errors immediately
  },
  // Add connection validation
  acquireConnectionTimeout: 60000,
  // Enable debug mode in development
  debug: process.env.NODE_ENV === 'development',
});

// Add connection pool monitoring
knex.on('query', (query) => {
  if (process.env.NODE_ENV === 'development') {
    global.logger?.debug('Knex Query: %o', { sql: query.sql, bindings: query.bindings });
  }
});

knex.on('query-error', (error, query) => {
  global.logger?.error('Knex Query Error: %o', {
    error: error.message,
    sql: query.sql,
    bindings: query.bindings
  });
});

// Monitor connection pool - simplified to avoid TypeScript issues
const logPoolStats = () => {
  try {
    // Simple connection test to monitor pool health
    knex.raw('SELECT 1 as health_check')
      .then(() => {
        global.logger?.debug('Database connection pool is healthy');
      })
      .catch((error) => {
        global.logger?.error('Database connection pool health check failed: %o', error.message);
      });
  } catch (error) {
    global.logger?.debug('Could not perform pool health check: %o', error.message);
  }
};

// Log pool health every 60 seconds in development
if (process.env.NODE_ENV === 'development') {
  setInterval(logPoolStats, 60000);
}

// Wrapper function to allow a KNEX query to use or not use a transaction
// Useful for model functions that may not always have multiple calls associated
knex.optionalTransacting = (call, trx) => {
  if (trx) return call.transacting(trx);
  return call;
};

// Enhanced wrapper with connection cleanup
knex.safeQuery = async (queryBuilder) => {
  try {
    const result = await queryBuilder;
    return result;
  } catch (error) {
    global.logger?.error('Database query failed: %o', error);
    throw error;
  }
};

// Graceful shutdown
process.on('SIGINT', async () => {
  global.logger?.info('Closing database connections...');
  await knex.destroy();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  global.logger?.info('Closing database connections...');
  await knex.destroy();
  process.exit(0);
});

module.exports = knex;
