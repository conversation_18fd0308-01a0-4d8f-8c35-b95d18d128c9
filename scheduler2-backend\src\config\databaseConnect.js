const config = require('@config/config');
const { types } = require('pg');

// Change type parser to return floats instead of strings for numeric the database
types.setTypeParser(types.builtins.NUMERIC, (value) => {
  return parseFloat(value);
});

let knex = require('knex');

knex = knex({
  client: 'pg',
  connection: config.db.connectionString,
  searchPath: ['knex', 'public'],
  pool: {
    min: 2, 
    max: 50, 
    acquireTimeoutMillis: 60000, 
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000, 
    idleTimeoutMillis: 300000, 
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 200, 
    propagateCreateError: false,
  },
  // Add connection validation
  acquireConnectionTimeout: 60000,
  // Enable debug mode in development
  debug: process.env.NODE_ENV === 'development',
});

// Basic error logging for database queries
knex.on('query-error', (error, query) => {
  global.logger?.error('Knex Query Error: %o', {
    error: error.message,
    sql: query.sql.substring(0, 200),
    bindings: query.bindings
  });
});

// Wrapper function to allow a KNEX query to use or not use a transaction
// Useful for model functions that may not always have multiple calls associated
knex.optionalTransacting = (call, trx) => {
  if (trx) return call.transacting(trx);
  return call;
};


module.exports = knex;
