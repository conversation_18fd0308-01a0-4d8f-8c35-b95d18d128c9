const createSubscriber = require('pg-listen');
const config = require('./config');
const databaseListenerModel = require('@models/databaseListenerModel');

const {
  db: { connectionString },
} = config;

// Accepts the same connection config object that the "pg" package would take
const subscriber = createSubscriber({ connectionString });

subscriber.notifications.on('refresh_bookings', async () => {
  // Payload as passed to subscriber.notify() (see below)
  await databaseListenerModel.refreshBookings();
});

subscriber.events.on('error', (error) => {
  console.error('Fatal database connection error:', error);
  global.logger.error('Fatal database connection error:  %o', error);
  throw Error('Fatal database connection error');
});

process.on('exit', () => {
  subscriber.close();
});

async function connect() {
  await subscriber.connect();
  await subscriber.listenTo('refresh_bookings');
}

module.exports = { connect, subscriber };
