const createSubscriber = require('pg-listen');
const config = require('./config');
const databaseListenerModel = require('@models/databaseListenerModel');

const {
  db: { connectionString },
} = config;

// Simple subscriber configuration - pg-listen has limited configuration options
const subscriber = createSubscriber({
  connectionString,
  // Only use the basic settings that pg-listen actually supports
  retryTimeout: 10000, // 10 seconds between retries
});

subscriber.notifications.on('refresh_bookings', async () => {
  try {
    // Payload as passed to subscriber.notify() (see below)
    await databaseListenerModel.refreshBookings();
    global.logger?.info('Successfully refreshed bookings from database notification');
  } catch (error) {
    global.logger?.error('Failed to refresh bookings: %o', error.message);
  }
});

// Simple error handling - just log, don't crash
subscriber.events.on('error', (error) => {
  console.error('Database listener error (non-fatal):', error.message);
  global.logger?.error('Database listener error (non-fatal): %o', error.message);
  // Do nothing else - let it fail silently
});

// Simple connection function - try once, if it fails, continue without it
async function connect() {
  try {
    global.logger?.info('Attempting to connect database listener...');
    await subscriber.connect();
    await subscriber.listenTo('refresh_bookings');
    global.logger?.info('Database listener connected successfully');
  } catch (error) {
    global.logger?.error('Database listener failed to connect: %o', error.message);
    global.logger?.info('Application will continue without database notifications');
    // Don't retry, just continue without the listener
  }
}

// Simple shutdown handling
process.on('exit', () => {
  try {
    subscriber.close();
  } catch (error) {
    // Ignore errors during shutdown
  }
});

module.exports = { connect, subscriber };
