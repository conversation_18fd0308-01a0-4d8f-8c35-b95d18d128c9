const createSubscriber = require('pg-listen');
const config = require('./config');
const databaseListenerModel = require('@models/databaseListenerModel');

const {
  db: { connectionString },
} = config;

// Configure subscriber with better reconnection settings
const subscriber = createSubscriber({
  connectionString,
  // Increase reconnection timeout from default 3s to 60s
  reconnectTimeout: 60000,
  // Retry connection attempts
  retryTimeout: 10000, // 10 seconds between retries
  // Add connection timeout settings
  connectionTimeoutMillis: 45000, // 45 seconds to establish connection
  // Keep alive settings
  keepAlive: true,
  keepAliveInitialDelayMillis: 0,
  // Parse connection string to get individual connection parameters for better control
  ...(connectionString.includes('postgresql://') && {
    // Add connection pool settings for the listener
    max: 1, // Only need 1 connection for listening
    idleTimeoutMillis: 300000, // 5 minutes
    query_timeout: 60000, // 60 seconds for queries
  })
});

subscriber.notifications.on('refresh_bookings', async () => {
  try {
    // Payload as passed to subscriber.notify() (see below)
    await databaseListenerModel.refreshBookings();
    global.logger?.info('Successfully refreshed bookings from database notification');
  } catch (error) {
    global.logger?.error('Failed to refresh bookings: %o', error.message);
  }
});

// Improved error handling - don't crash the application
subscriber.events.on('error', (error) => {
  console.error('Database listener connection error:', error.message);
  global.logger?.error('Database listener connection error: %o', error.message);

  // Don't throw error - let the subscriber handle reconnection
  // The application should continue running even if the listener fails
});

// Handle reconnection events
subscriber.events.on('reconnect', () => {
  global.logger?.info('Database listener reconnected successfully');
});

// Handle connection events
subscriber.events.on('connected', () => {
  global.logger?.info('Database listener connected');
});

// Handle disconnection events
subscriber.events.on('disconnected', () => {
  global.logger?.warn('Database listener disconnected');
});

process.on('exit', () => {
  subscriber.close();
});

// Enhanced connection function with retry logic
async function connect(retryCount = 0, maxRetries = 5) {
  try {
    global.logger?.info('Attempting to connect database listener...');
    await subscriber.connect();
    await subscriber.listenTo('refresh_bookings');
    global.logger?.info('Database listener connected and listening to refresh_bookings');
  } catch (error) {
    global.logger?.error('Database listener connection failed (attempt %d/%d): %o',
      retryCount + 1, maxRetries, error.message);

    if (retryCount < maxRetries) {
      const delay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Exponential backoff, max 30s
      global.logger?.info('Retrying database listener connection in %dms...', delay);

      setTimeout(() => {
        connect(retryCount + 1, maxRetries).catch((retryError) => {
          global.logger?.error('Database listener retry failed: %o', retryError.message);
        });
      }, delay);
    } else {
      global.logger?.error('Database listener failed to connect after %d attempts. Application will continue without notifications.', maxRetries);
    }
  }
}

// Graceful shutdown
process.on('SIGINT', async () => {
  global.logger?.info('Closing database listener...');
  await subscriber.close();
});

process.on('SIGTERM', async () => {
  global.logger?.info('Closing database listener...');
  await subscriber.close();
});

module.exports = { connect, subscriber };
