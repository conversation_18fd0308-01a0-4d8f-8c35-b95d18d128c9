const { KMSClient, DecryptCommand } = require('@aws-sdk/client-kms');
const { S3Client, GetObjectCommand } = require('@aws-sdk/client-s3');
const { appEnv, awsRegion } = require('./config');
const environment = appEnv === 'production' ? 'prod' : 'dev';
const s3 = new S3Client({
  // The key s3ForcePathStyle is renamed to forcePathStyle.
  forcePathStyle: true,
  region: awsRegion,
});
const jsforce = require('jsforce');

const keys = {
  application: {
    dev: 'sandbox/salesforce-client_secret.json',
    prod: 'production/salesforce-client_secret.json',
  },
  access: {
    dev: 'sandbox/SBXaccessTokenEncrypted',
    prod: 'production/PRODaccessTokenEncrypted',
  },
  refresh: {
    dev: 'sandbox/SBXrefreshTokenEncrypted',
    prod: 'production/PRODrefreshTokenEncrypted',
  },
};

const getSalesforceInstance = async (environment) => {
  const errorCallback = (error) => {
    if (error) throw error;
  };

  const sfApplicationParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.application[environment],
  };

  let applicationResponse = null;
  try {
    applicationResponse = await s3.send(new GetObjectCommand(sfApplicationParams));
  } catch (error) {
    console.log('error');
    console.log(error);
  }

  if (!applicationResponse) {
    throw new Error(
      `Failed to retrieve Salesforce application credentials for environment: ${environment}`
    );
  }

  //create Oauth2 object
  const appInfo = JSON.parse(await applicationResponse.Body.transformToString());
  const {
    client_secret: clientSecret,
    client_id: clientId,
    loginUrl,
    redirectUri,
    instanceUrl,
  } = appInfo;

  const oauth2 = new jsforce.OAuth2({
    loginUrl,
    clientId,
    clientSecret,
    redirectUri,
  });

  const sfAccessParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.access[environment],
  };

  const accessResponse = await s3.send(new GetObjectCommand(sfAccessParams));

  const accessTokenParams = {
    CiphertextBlob: await accessResponse.Body.transformToByteArray(),
  };

  const sfRefreshParams = {
    Bucket: 'salesforce-authentication',
    Key: keys.refresh[environment],
  };

  const refreshResponse = await s3.send(new GetObjectCommand(sfRefreshParams));

  const refreshTokenParams = {
    CiphertextBlob: await refreshResponse.Body.transformToByteArray(),
  };

  const kms = new KMSClient({
    region: awsRegion,
  });

  const { Plaintext: decryptedRefresh } = await kms.send(new DecryptCommand(refreshTokenParams));
  const { Plaintext: decryptedAccess } = await kms.send(new DecryptCommand(accessTokenParams));

  const accessToken = Buffer.from(decryptedAccess).toString();
  const refreshToken = Buffer.from(decryptedRefresh).toString();

  const salesforceConnection = new jsforce.Connection({
    oauth2,
    accessToken,
    refreshToken,
    instanceUrl,
  });

  salesforceConnection.on('error', errorCallback);

  global.logger.info(`Connected to ${environment} Salesforce`);
  return salesforceConnection;
};

const connectSalesforce = async () => {
  return getSalesforceInstance(environment);
};

const salesforceConnection = connectSalesforce();

module.exports = { salesforceConnection };
