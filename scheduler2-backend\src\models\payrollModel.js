const config = require('@config/config');
const db = require('@config/databaseConnect');
const fs = require('fs');
const moment = require('moment');
const { keysToCamelCase } = require('@utils');

const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const { GetObjectCommand, S3Client, PutObjectCommand } = require('@aws-sdk/client-s3');

const s3 = new S3Client({
  credentials: {
    accessKeyId: config.awsAccessKeyId,
    secretAccessKey: config.awsAccessSecretKey,
  },
  region: config.awsRegion,
});

// Values are Salesforce transaction rest service keys
const mtFileHeaders = [
  'ContractID',
  'DateDeposited',
  'CheckAmount',
  'CheckNumber',
  'Hcclass',
  'uniqueId',
  'EnteredByEmail',
];

// Keys are Payzer file header, Values are the values Salesforce rest service.
const payzerFileHeadersDict = {
  LAST4: 'Last4',
  AMOUNT: 'Amount',
  MEMO: 'Memo',
  'PAYMENT DATE': 'PaymentDate',
  'PAYMENT NETWORK': 'PaymentNetwork',
  uniqueId: 'DbId',
  EnteredByEmail: 'EnteredByEmail',
  'TAKEN BY': 'CollectedBy',
  PaymentType: 'PaymentType',
  STATUS: 'Status',
  NOTES: 'Notes',
  'BATCH TOTAL': 'BatchTotal',
};

// Getting Date dropdown values
const getFolderNamesFromS3 = async () => {
  const s3Params = {
    Bucket: config.financePayrollBucketName,
  };
  let uploadedDates = [];
  // eslint-disable-next-line no-useless-catch
  try {
    const { Contents } = await s3.listObjectsV2(s3Params);
    Contents.forEach((el) => {
      const key = el.Key.split('/')[0];
      if (!uploadedDates.includes(key)) {
        uploadedDates.push(key);
      }
    });
    uploadedDates = uploadedDates.sort((a, b) => new Date(b) - new Date(a)).slice(0, 5);
    return uploadedDates;
  } catch (error) {
    throw error;
  }
};

// Get uploaded documents from S3
const getUploadedDocumentsFromS3 = async (bucketPrefix) => {
  const s3Params = {
    Bucket: config.financePayrollBucketName,
    Prefix: bucketPrefix,
  };
  const uploadedDocs = [];
  const combinedDoc = [];
  // eslint-disable-next-line no-useless-catch
  try {
    const { Contents } = await s3.listObjectsV2(s3Params);
    if (Contents.length) {
      Contents.forEach((doc) => {
        const fileName = doc.Key.split('/').pop();
        if (!fileName.startsWith('deleted_') && fileName.length > 0) {
          const formatedDate = moment(doc.LastModified).format('MMM Do YYYY');
          const filename = fileName.split('.').shift();
          if (filename === 'combinedPayroll') {
            combinedDoc.push({ fileName: filename, lastModified: formatedDate });
          } else {
            uploadedDocs.push({ fileName: filename, lastModified: formatedDate });
          }
        }
      });
    }
    return { uploadedDocs, combinedDoc };
  } catch (error) {
    throw error;
  }
};

// Upload File - Rename File as we upload
const uploadFile = async (path, type, bucketUrl, fileName) => {
  // eslint-disable-next-line no-useless-catch
  try {
    // eslint-disable-next-line no-sync
    const data = await fs.readFileSync(path);
    const s3Params = {
      Bucket: bucketUrl,
      Key: fileName,
      Body: data,
      ContentType: type,
    };
    await s3.send(new PutObjectCommand(s3Params));
    return true;
  } catch (error) {
    throw error;
  }
};

const getS3Url = async (bucketName, fileKey) => {
  try {
    return getSignedUrl(
      s3,
      new GetObjectCommand({
        Bucket: config.financePayrollBucketName,
        Key: `${bucketName}/${fileKey}.csv`,
      }),
      {
        expiresIn: 60,
      }
    );
  } catch (error) {
    error.params = { bucketName, fileKey };
    return error;
  }
};

// Money collection
const handleMoneyCollectionData = async (fileData, fileName) => {
  const sfParams = {};
  // Handle M&T file
  if (fileName === 'MT') {
    sfParams.fileName = 'MT';
    const data = handleMTFile(mtFileHeaders, fileData);
    sfParams.lstMTPayrollModel = data;
  }
  // Handle Payzer file
  if (fileName === 'Payzer') {
    sfParams.fileName = 'Payzer';
    const data = handlePayzerFile(payzerFileHeadersDict, fileData);
    sfParams.lstPayzerPayrollModel = data;
  }
  return sfParams;
};

// Create Salesforce params for Payzer file data
const handlePayzerFile = (payzerFileHeadersDict, data) => {
  const parsedDataArr = [];
  for (let k = 0; k < data.length; k++) {
    const fileRecord = data[k];
    // If for some reason the row is invalid, go to the next one
    if (!fileRecord) continue;
    const transactionObject = {};
    const recordKeys = Object.keys(fileRecord);
    recordKeys.forEach((key) => {
      if (payzerFileHeadersDict[key]) {
        // Need to format date to match what Salesforce is expecting
        if (key === 'PAYMENT DATE') {
          transactionObject[payzerFileHeadersDict[key]] = moment(fileRecord[key]).format(
            'YYYY-MM-DD'
          );
        } else {
          transactionObject[payzerFileHeadersDict[key]] = fileRecord[key];
        }
      }
    });
    parsedDataArr.push(transactionObject);
  }
  return parsedDataArr;
};

// Create Salesforce params for M&T file data
const handleMTFile = (mtFileHeaders, data) => {
  const parsedDataArr = [];
  for (let k = 0; k < data.length; k++) {
    const fileRecord = data[k];
    // If for some reason the row is invalid, go to the next one
    if (!fileRecord) continue;
    const recordKeys = Object.keys(fileRecord);
    const transactionObject = {};
    if (!recordKeys.length) continue;
    recordKeys.forEach((key) => {
      if (mtFileHeaders.includes(key)) {
        // Match uniqueID to the key Salesforce is expecting
        if (key === 'uniqueId') {
          transactionObject.DbId = fileRecord[key];
        } else {
          transactionObject[key] = fileRecord[key];
        }
      }
    });
    parsedDataArr.push(transactionObject);
  }
  return parsedDataArr;
};

// Check if record exists for file row
const checkForMoneyCollectionRecord = async (record, fileName) => {
  const { uniqueId } = record;
  const { rows } = await db.raw(
    `
    SELECT
      id,
      name,
      customer_id,
      sf_id,
      amount,
      payment_date,
      payment_number,
      payment_network,
      hcclass,
      unique_id,
      transaction_id,
      collected_by,
      entered_by_email,
      status,
      invoice_number,
      batch_total
    FROM
      money_collection_records
    WHERE
      unique_id = ?
    AND
      type = ?
  `,
    [uniqueId, fileName]
  );
  return rows?.length > 0 ? keysToCamelCase(rows[0]) : false;
};

// Insert money collection record into database
const insertRecord = async (recordsList, fileName) => {
  const wildcardQueryStringParts = [];
  const wildcardArray = [];
  if (fileName === 'MT') {
    recordsList.forEach((record) => {
      wildcardArray.push(
        record.Customer,
        record['SITE ID'],
        record.ContractID,
        record.CheckAmount,
        record.DateDeposited,
        record.CheckNumber,
        record.Hcclass,
        record.uniqueId,
        fileName,
        record.EnteredByEmail
      );
      wildcardQueryStringParts.push('?, ?, ?, ?, ?, ?, ?, ?, ?, ?');
    });
    await db.raw(
      `
      INSERT INTO money_collection_records(name, customer_id, sf_id, amount, payment_date, payment_number, hcclass, unique_id, type, entered_by_email)
      VALUES (${wildcardQueryStringParts.join('), (')})
    `,
      wildcardArray
    );
    return true;
  }
  if (fileName === 'PAYZER') {
    recordsList.map((record) => {
      wildcardArray.push(
        record['DISPLAY NAME'],
        record['CUSTOMER NUMBER'],
        record.MEMO,
        record.AMOUNT,
        record['PAYMENT DATE'],
        record.LAST4,
        record['PAYMENT NETWORK'],
        record.uniqueId,
        fileName,
        record.EnteredByEmail,
        record['TAKEN BY'],
        record.STATUS,
        record['INVOICE NUMBER'],
        record['BATCH TOTAL']
      );
      wildcardQueryStringParts.push('?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?');
      return true;
    });
    await db.raw(
      `
      INSERT INTO money_collection_records(name, customer_id, sf_id, amount, payment_date, payment_number, payment_network, unique_id, type, entered_by_email, collected_by, status, invoice_number, batch_total)
      VALUES (${wildcardQueryStringParts.join('), (')})
    `,
      wildcardArray
    );
    return true;
  }
};

// Update database record with transaction ID
// Records with transaction IDs indicate this record has been successfully processed
const updateRecordsWithTransactionId = async (successObj) => {
  const uniqueIdsArr = Object.keys(successObj);
  if (!uniqueIdsArr.length) return;
  for (let k = 0; k < uniqueIdsArr.length; k++) {
    const uniqueId = uniqueIdsArr[k];
    await db.raw(
      `
      UPDATE
        money_collection_records
      SET
        transaction_id = ?
      WHERE
        unique_id = ?
    `,
      [successObj[uniqueId], uniqueId]
    );
  }
  return;
};

// Check database for settled record for returnedRecord passed
const checkForSettledRecord = async (returnedRecord) => {
  // We are looking for a settle record which will have the positive amount
  const amountToCheck = Number(returnedRecord.AMOUNT) * -1;
  const { rows } = await db.raw(
    `
    SELECT
      sf_id
    FROM
      money_collection_records
    WHERE
      status = 'Settled'
    AND
      type = 'PAYZER'
    AND
      name = ?
    AND
      invoice_number = ?
    AND
      customer_id = ?
    AND
      collected_by = ?
    AND
      amount = ?
    AND
      transaction_id IS NOT NULL
  `,
    [
      returnedRecord['DISPLAY NAME'],
      returnedRecord['INVOICE NUMBER'],
      returnedRecord['CUSTOMER NUMBER'],
      returnedRecord['TAKEN BY'],
      `${amountToCheck}`,
    ]
  );
  return rows;
};

module.exports = {
  getFolderNamesFromS3,
  getUploadedDocumentsFromS3,
  getS3Url,
  handleMoneyCollectionData,
  uploadFile,
  checkForMoneyCollectionRecord,
  insertRecord,
  updateRecordsWithTransactionId,
  checkForSettledRecord,
};
