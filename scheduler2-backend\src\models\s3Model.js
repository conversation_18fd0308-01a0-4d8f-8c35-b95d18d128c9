const config = require('@config/config');
const { S3Client, DeleteObjectCommand, CopyObjectCommand } = require('@aws-sdk/client-s3');

const s3 = new S3Client({
  credentials: {
    accessKeyId: config.awsAccessKeyId,
    secretAccessKey: config.awsAccessSecretKey,
  },
  region: config.awsRegion,
});

// Delete File
// After copying file over we will delete original file.
const deleteFile = async (s3DeleteParams) => {
  try {
    await s3.send(new DeleteObjectCommand(s3DeleteParams));
    return { success: true };
  } catch (error) {
    error.params = s3DeleteParams;
    throw error;
  }
};

// Copy file to rename deleted_timeStamp_fileName
const copyFile = async (s3CopyObjectParams) => {
  try {
    const response = await s3.send(new CopyObjectCommand(s3CopyObjectParams));
    return response;
  } catch (error) {
    error.params = s3CopyObjectParams;
    throw error;
  }
};

module.exports = {
  deleteFile,
  copyFile,
};
