let { salesforceConnection: salesforceConnMA } = require('@config/salesforceConnect');
const { getSalesforceAxiosInstance } = require('@config/salesforceAxiosConfig');
const { keysToCamelCase } = require('@utils/camelCaseConverter');
const utilityModel = require('./utilityModel');
const axios = require('axios');
const config = require('@config/config');

salesforceConnMA.then((conn) => {
  salesforceConnMA = conn;
});

const upsertObject = async (objectType, insertObject) => {
  let id;
  if (!insertObject.Id) ({ id } = await createObject(objectType, insertObject));
  else ({ id } = await updateObject(objectType, insertObject));

  return id;
};

const createObject = async (objectType, insertObject) => {
  // '/' at the end to fix jsforce upsert bug
  try {
    const { id } = await salesforceConnMA.sobject(objectType).create(insertObject);
    return { id };
  } catch (error) {
    error.params = { objectType, insertObject };
    if (error.name === 'INVALID_CROSS_REFERENCE_KEY')
      error.message = "Owner's Salesforce ID is invalid";
    throw error;
  }
};

const eaService = async (action, sfObjects, key) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = 'EAService/';
    let returnId;
    for (let i = 0; i < sfObjects.length; i++) {
      const sfObject = sfObjects[i];
      const params = {
        Operation: action,
        [key]: [sfObject],
      };
      const response = await salesforceAxiosInstance({ url, data: params });
      returnId = response?.data;
    }
    return returnId;
  } catch (error) {
    error.params = { sfObjects };
    if (error?.response?.data) throw new Error(`Error on Salesforce:\n${error.response.data}`);
    throw error;
  }
};

const wxService = async (data) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = 'WxVisitOperations/';
    const response = await salesforceAxiosInstance({ url, data });
    return JSON.parse(response?.data);
  } catch (error) {
    error.params = { data };
    throw error;
  }
};

const barrierService = async (data) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = 'BarrierServices/';
    await salesforceAxiosInstance({ url, data });
  } catch (error) {
    error.params = { data };
    const parsedData = JSON.parse(error?.response?.data || '');
    const message = parsedData?.[0]?.message || '';
    throw message.length > 0 ? message : error;
  }
};

const getObject = async (objectName, id) => {
  try {
    const response = await salesforceConnMA.sobject(objectName).retrieve(id);
    return { sfObject: response };
  } catch (error) {
    error.params = { objectName, id };
    throw error;
  }
};

// This is not currently used anywhere
const deleteObject = async (objectType, objectId) => {
  try {
    await salesforceConnMA.sobject(objectType).destroy(objectId);
    return { objectId };
  } catch (error) {
    error.params = { objectType, objectId };
    throw error;
  }
};

/**
 *
 * @param {string} objectType the API name of the SF object to be updated
 * @param {object or array} updateObjects object or objects to be updated, must have Id property for SF
 */
const updateObjects = async (objectType, updateObjects) => {
  try {
    const { id } = await salesforceConnMA.sobject(objectType).update(updateObjects, 'Id');
    return id;
  } catch (error) {
    if (error.errorCode === 'INVALID_CROSS_REFERENCE_KEY')
      error.message = "Couldn't find that visit on Salesforce!";
    error.params = { updateObjects };
    throw error;
  }
};

/**
 * Same as update objects under the hood
 * JSForce can take multiple objects
 * but this will allow using the singular verbage
 */
const updateObject = updateObjects;

const getAccountIdWithDealId = async (dealId) => {
  try {
    const [{ Account__c: accountId }] = await salesforceConnMA
      .sobject('Deal__c')
      .select('Account__c')
      .where(`Id='${dealId}'`);

    return accountId;
  } catch (error) {
    return { error: "Couldn't find an account for that deal", dealId };
  }
};

const getAccountIdWithOperationsId = async (operationsId) => {
  try {
    const [{ Account__c: accountId }] = await salesforceConnMA
      .sobject('Operations__c')
      .select('Account__c')
      .where(`Id='${operationsId}'`);
    return accountId;
  } catch (error) {
    return { error: "Couldn't find an account for that operationsId", operationsId };
  }
};

const getContactInfoWithAccountId = async (accountId) => {
  const [accountResponse] = await salesforceConnMA
    .sobject('Account')
    .select('Primary_Contact__c, Day_Phone__c, Evening_Phone__c, BillingAddress, Email__c')
    .where(`Id='${accountId}'`);

  if (!accountResponse) throw new Error('Couldnt find an account for that ID');

  const {
    Primary_Contact__c: contactId,
    BillingAddress: billingAddress,
    Email__c: email,
  } = accountResponse;
  let { Day_Phone__c: phoneNumber, Evening_Phone__c: secondaryPhoneNumber } = accountResponse;

  if (phoneNumber) phoneNumber = phoneNumber.replace(/[^\d]/g, ''); // remove all but numbers from phone numbers
  if (secondaryPhoneNumber) secondaryPhoneNumber = secondaryPhoneNumber.replace(/[^\d]/g, ''); // remove all but numbers from phone numbers

  if (!contactId || contactId === 'null') throw new Error("Couldn't find contact name for account");
  const [
    {
      Name: customerName,
      MailingAddress: { latitude, longitude },
    },
  ] = await salesforceConnMA
    .sobject('Contact')
    .select('Name, MailingAddress')
    .where(`Id='${contactId}'`);

  return {
    customerName,
    phoneNumber,
    email,
    address: { ...billingAddress, latitude, longitude },
  };
};

const getRecordTypeIdWithTypeName = async (typeName) => {
  try {
    const response = await salesforceConnMA.query(`SELECT Id 
      FROM RecordType WHERE Name = ${typeName}`);
    return {
      recordTypeId: response.Id,
    };
  } catch (error) {
    error.params = { typeName };
    throw error;
  }
};

const getWxVisitInfo = async (visitId) => {
  try {
    const [response] = await salesforceConnMA
      .sobject('Wx_Visit__c')
      .select('Name')
      .where(`Id='${visitId}'`);

    const { Name: visitName } = response;
    return { visitName };
  } catch (error) {
    error.params = { visitId };
    throw error;
  }
};

// Update resulting questions and answers on Salesforce.
const updateSfResultingQuestions = async (sfObjName, sfObjUpdateParams) => {
  try {
    const response = await salesforceConnMA.sobject(sfObjName).update(sfObjUpdateParams);
    return response;
  } catch (error) {
    error.params = { sfObjName, sfObjUpdateParams };
    throw error;
  }
};

// Returns all fields and values from Salesforce.
const returnResultStatusFromSf = async (sfObjectToLookAt, sfObjectId) => {
  try {
    const response = await salesforceConnMA.sobject(sfObjectToLookAt).retrieve(sfObjectId);
    return response;
  } catch (error) {
    error.params = { sfObjectToLookAt, sfObjectId };
    throw error;
  }
};

// Update missing documents checkbox on salesforce. Used for ready for scheduling.
const updateMissingDocsStatus = async (sfObjectName, sfUpdateObj) => {
  try {
    const response = await salesforceConnMA.sobject(sfObjectName).update(sfUpdateObj);
    return response;
  } catch (error) {
    error.params = { sfObjectName, sfUpdateObj };
    throw error;
  }
};

const getWorkLineItemsWithOpsId = async (operationsId) => {
  try {
    const response = await salesforceConnMA
      .sobject('Work_Line_Item__c')
      .find({ Operations__c: operationsId }, 'Type__c, Line_Item_Name__c');
    // Cleaning Up Salesforce Field Names
    return response.map(({ Type__c: type, Line_Item_Name__c: name }) => {
      return { type, name };
    });
  } catch (error) {
    console.log('Failed to get Work Line Items for Operations Id: ', operationsId);
    return [];
  }
};

const getAttributeDict = (attributesArr) => {
  return attributesArr.reduce(
    (accum, curr) => ((accum[curr.attributeName] = curr.attributeId), accum),
    {}
  );
};

const getAttributes = async (params) => {
  const { operationsId, leadVendor, otherLeadSource2 } = params;
  try {
    const attributes = [];
    const workLineItems = await getWorkLineItemsWithOpsId(operationsId);
    const insulationAttributes = await utilityModel.getJobAttributesByEventType('0005');
    const attributeDict = getAttributeDict(insulationAttributes);
    // TODO: Seperate or Add conditional/switch statement to handle multiple departments with attributes are added for other departments
    const attributeIds = {
      aluminumWalls: attributeDict['Aluminum Walls'],
      capInstaller: attributeDict['CAP Installer'],
      homeDepot: attributeDict['Home Depot'],
    };

    if (workLineItems?.length) {
      for (let k = 0; k < workLineItems.length; k++) {
        const { name } = workLineItems[k];
        // Aluminum Walls
        if (!attributes.includes(attributeIds.aluminumWalls)) {
          if (name?.includes('Walls - Aluminum')) {
            attributes.push(attributeIds.aluminumWalls);
          }
        }
      }
    }
    if (leadVendor === 'CAP') attributes.push(attributeIds.capInstaller);
    if (otherLeadSource2 === 'Home Depot') attributes.push(attributeIds.homeDepot);
    return attributes;
  } catch (error) {
    console.error('Error with getting attributes: ', error);
    return [];
  }
};

const getInsulationEventInfoWithOperationsId = async (operationsId) => {
  try {
    const response = await salesforceConnMA
      .sobject('Operations__c')
      .select(
        `Site_ID_Ops__c, 
        Lead_Vendor__c, 
        Job_Status__c, 
        Final_Contract_Amount__c, 
        Day_Phone_2__c, 
        Evening_Phone_2__c, 
        Notes_for_Crew_from_Deals__c, 
        Operations_Insulation_Notes__c, 
        Insulation_Impromptu_Days__c, 
        Estimated_Job_Length_Days2__c, 
        Account__c,
        Deal__c,
        Insulation_Installer__c,
        Other_Lead_Source_2__c,
        Sum_Revised_Contract_Amount__c,
        Mass_Save_Amount_at_Approval__c
        `
      )
      .where(`Id='${operationsId}'`);

    if (!response.length) throw Error("Couldn't find an Operations page for that ID!");

    const [
      {
        Site_ID_Ops__c: siteId,
        Lead_Vendor__c: leadVendor,
        Job_Status__c: jobStatus,
        Final_Contract_Amount__c: amount,
        Day_Phone_2__c: phoneNumber,
        Evening_Phone_2__c: secondaryPhoneNumber,
        Insulation_Impromptu_Days__c: impromptuDays,
        Estimated_Job_Length_Days2__c: estimatedJobLength,
        Notes_for_Crew_from_Deals__c: notesForCrew,
        Operations_Insulation_Notes__c: insulationNotes,
        Account__c: accountId,
        Deal__c: dealId,
        Insulation_Installer__c: insulationInstaller,
        Other_Lead_Source_2__c: otherLeadSource2,
        Sum_Revised_Contract_Amount__c: revisedContractAmount,
        Mass_Save_Amount_at_Approval__c: finalContractAmountAtApproval,
      },
    ] = response;
    const attributeParams = { operationsId, leadVendor, otherLeadSource2 };

    const parsedAttributes = await getAttributes(attributeParams);

    const hweInstallers = ['HomeWorks Energy', 'HomeWorks Energy CAP'];

    const opsIdErrorMessage = !hweInstallers.includes(insulationInstaller)
      ? 'Insulation Installer for this Id is not HomeWorks Energy or HomeWorks Energy CAP on Salesforce Operation Object'
      : null;

    const responseObject = {
      siteId,
      jobStatus,
      leadVendor,
      amount,
      phoneNumber,
      secondaryPhoneNumber,
      impromptuDays,
      estimatedJobLength,
      notes: {
        fieldNotes: notesForCrew || '',
        officeNotes: insulationNotes || '',
      },
      sfIds: {
        accountId,
        operationsId,
        dealId,
      },
      attributes: parsedAttributes,
      formFieldErrors: { 'sfIds.operationsId': opsIdErrorMessage },
      revisedContractAmount,
      finalContractAmountAtApproval,
    };

    return responseObject;
  } catch (error) {
    error.params = operationsId;
    throw error;
  }
};

const getHEAEventInfoWithDealId = async (dealId, skipCheck = false) => {
  const response = await salesforceConnMA
    .sobject('Deal__c')
    .select(
      `Site_ID_Deal__c, 
      Name,
      Lead_Vendor__c, 
      CAP_Billing_Lead_Vendor__c,
      Account__c,
      Deal_ID__c,
      Operations__c,
      Heating_Type__c,
      Gas_Provider__c,
      Electric_Provider_2__c,
      Units_In_Building__c,
      Appliance_Performed__c,
      Approval_Software__c`
    )
    .where(`Id='${dealId}'`);

  if (!response.length) throw Error("Couldn't find a deal page for that ID!");

  const programDict = {
    Eversource: 'EVR',
    'National Grid': 'NG',
    'Columbia Gas': 'CMA',
    'Cape Light': 'CL',
  };

  const [
    {
      Site_ID_Deal__c: siteId,
      Name: dealName,
      Lead_Vendor__c: leadVendor,
      CAP_Billing_Lead_Vendor__c: capBillingLeadVendor,
      Account__c: accountId,
      Deal_ID__c: fullDealId,
      Operations__c: operationsId,
      Heating_Type__c: fuelType,
      Gas_Provider__c: gasProvider,
      Electric_Provider_2__c: electricProvider,
      Units_In_Building__c: unitsInBuilding,
      Appliance_Performed__c: appliancePerformed,
      Approval_Software__c: approvalSoftware,
    },
  ] = response;

  const opportunityDetails = await salesforceConnMA
    .sobject('Opportunity')
    .select('Id, Name')
    .where(`AccountId='${accountId}'`);

  const hvacInstallOpportunity = opportunityDetails.filter(({ Name }) => {
    return Name.includes('HVAC Install');
  });

  let opportunityId = null;
  if (hvacInstallOpportunity.length > 0) [{ Id: opportunityId }] = hvacInstallOpportunity;

  // Used if you just need one value from this function. You can add more values to the return below if needed
  if (skipCheck) return { appliancePerformed };

  if (!fuelType)
    throw Error(
      'Please fill out the "Heating Type" on the Salesforce Account page, then try again'
    );
  if (!leadVendor)
    throw Error('Please fill out the "Lead Vendor" on the Salesforce Deal page, then try again');
  if (fuelType === 'Gas' && !gasProvider)
    throw Error(
      'Please fill out the "Gas Provider" on the Salesforce Operations page, then try again'
    );
  else if (!electricProvider)
    throw Error(
      'Please fill out the "Electric Provider" on the Salesforce Operations page, then try again'
    );

  const program = fuelType === 'Gas' ? programDict[gasProvider] : programDict[electricProvider];

  if (!program)
    throw Error(
      'There was an error determining the customer\'s program. Please verify their "Gas or Electric Provider" (Operations page) and their "Heating Type" (Account page).'
    );

  const responseObject = {
    leadVendor,
    capBillingLeadVendor: capBillingLeadVendor || '',
    sfIds: {
      accountId,
      dealId: fullDealId,
      dealName,
      operationsId,
      siteId,
      opportunityId,
    },
    fuelType: fuelType.toLowerCase(),
    program,
    unitsInBuilding,
    appliancePerformed,
    approvalSoftware,
  };

  return responseObject;
};

const getSiteEvalDetails = async (opportunityId) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = 'opportunityDetails/';
    const params = { opportunityId };
    const { data: opportunityDetails } = await salesforceAxiosInstance({ url, data: params });

    if (!opportunityDetails) throw Error('Problem getting Opportunity details from Salesforce');

    return JSON.parse(opportunityDetails);
  } catch (error) {
    console.log('ERROR:', error?.response?.data);
    console.log('params:', { opportunityId });
    if (error?.request?.res?.statusCode === 400) throw Error('Please pass valid  Opportunity id.');
    if (error?.request?.res?.statusCode === 500)
      throw Error(error?.response?.data?.[0]?.message || 'Problem getting Opportunity details.');
    error.params = { opportunityId };
    throw Error(error);
  }
};

const hvacSiteEvalOperation = async (data) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = 'siteEval/';
    const response = await salesforceAxiosInstance({ url, data });
    if (!response) throw Error('Salesforce Failed to Perform the Operation.');

    return JSON.parse(response.data);
  } catch (error) {
    console.log('ERROR:', error?.response?.data);
    console.log('params:', data);
    if (error?.request?.res?.statusCode === 400) throw Error('Please pass valid  salesforce id.');
    error.params = { data };
    throw Error(error);
  }
};

const hvacSalesOperation = async (data) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = 'SalesVisit/';
    const response = await salesforceAxiosInstance({ url, data });
    return JSON.parse(response.data);
  } catch (error) {
    console.log('ERROR:', error?.response?.data);
    console.log('params:', data);
    if (error?.request?.res?.statusCode === 400) throw Error('Please pass valid Salesforce id.');
    error.params = { data };
    throw error;
  }
};

/**
 * Gets salesforce information for work orders and work visits
 *
 * @param {array} params work order ids to be fetched
 * @returns {object} object containing parsed work orders and account id
 */
const getWorkOrders = async (params) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();

    const url = 'ReturnWorkOrders/';
    const { data: workOrders } = await salesforceAxiosInstance({ url, data: params });

    if (workOrders.length === 0) throw Error('There is No Work Order associated to this Id');

    const workOrderDetails = workOrders.map(
      ({
        Id: workOrderId,
        Equipment_Order_Notes__c: equipmentOrderNotes,
        Equipment_Order_Status__c: equipmentOrderStatus,
        Work_Order_Line_Items__r: woli,
        Work_Visits__r: workVisits,
        Contract__r: contractDetails,
      }) => {
        let workOrderObj = {
          equipmentOrderNotes,
          equipmentOrderStatus,
        };
        // TODO: remove when sf route is updated to only return uncanceled visits
        const uncanceledWorkVisits = workVisits?.records.filter(
          (visit) =>
            !visit.Name?.includes('Canceled') &&
            !visit.Name?.includes('Rescheduled') &&
            !visit.Visit_Status__c?.includes('Complete')
        );

        if (uncanceledWorkVisits?.length) {
          if (uncanceledWorkVisits.length > 1) {
            throw Error(
              `There are multiple open visits associated with a work order for that customer. 
              Please paste this error into a ticket for software. 
              Work visits: ${uncanceledWorkVisits.map((wv) => wv.Id).join(', ')}`
            );
          }

          const [workVisit] = uncanceledWorkVisits;

          workOrderObj = {
            ...workOrderObj,
            workOrderId,
            workVisitId: workVisit.Id,
            workVisitName: workVisit.Work_Visit_Name__c,
            crewName: workVisit.Crew_Name__c,
            startDate: workVisit.Visit_Start_Date__c,
            endDate: workVisit.Visit_End_Date__c,
            projectManager: contractDetails?.Project_Manager__c || '',
            concierge: contractDetails?.Account_Manager__c || '',
          };
        }
        if (woli) {
          const product = woli.records[0].Product__r;

          if (!product)
            throw Error(
              `There is no product associated with a work line item for this customer.
              Please paste this error into a ticket for software.
              Work line item: ${woli.records[0].Id}
            `
            );

          workOrderObj = {
            ...workOrderObj,
            workOrderId,
            workLineItemId: woli.records[0].Id,
            productId: product.Id,
            eventType: product.ProductCode,
            eventTypeName: product.Name,
            projectManager: contractDetails?.Project_Manager__c || '',
            concierge: contractDetails?.Account_Manager__c || '',
          };
        }
        return workOrderObj;
      }
    );
    return { accountId: workOrders[0].Account__c, workOrders: workOrderDetails };
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const hvacInstallOperation = async (params) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();

    const url = 'HvacWorkVisit/';

    const response = await salesforceAxiosInstance({ url, data: params });

    return response.data;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const updateWorkOrder = async (params) => {
  const salesforceAxiosInstance = await getSalesforceAxiosInstance();

  const url = 'UpdateWorkOrder/';
  const response = await salesforceAxiosInstance({ url, data: params });

  return response.data;
};

/**
 * Swap event on Salesforce
 *
 * @param {Array} params
 * Ex: [{ sfEventId: '00U4X00000xg33X', newHesSfId: '005E0000005c7tqIAA'}]
 * sfEventId: String - Salesforce Event Id
 * newHesSfId: String - Salesforce User's Id
 */
const swapEvents = async (params) => {
  try {
    for (let k = 0; k < params.length; k++) {
      const { sfEventId, newHesSfId } = params[k];
      await salesforceConnMA.sobject('Event').update({ Id: sfEventId, OwnerId: newHesSfId });
    }
    return true;
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const getInsulationEventsInfoWithOperationsIds = async (operationsIds) => {
  try {
    const { records: response } = await salesforceConnMA.query(
      `
      SELECT 
        Unique_ID__c,
        Account__c,
        Final_Contract_Amount__c
      FROM
        Operations__c
      WHERE
        Id IN (${operationsIds.join(',')})`
    );

    const responseObjectArray = response.map((salesforceInfo) => {
      const {
        Unique_ID__c: operationsId,
        Account__c: accountId,
        Final_Contract_Amount__c: amount,
      } = salesforceInfo;

      return { operationsId, accountId, amount };
    });

    return responseObjectArray;
  } catch (error) {
    error.params = operationsIds;
    throw error;
  }
};

// Invoke Salesforce money collection rest api
const handleMoneyCollection = async (params) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();

    const url = 'PayrollService/';
    const response = await salesforceAxiosInstance({ url, data: params });

    return response.data;
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const getAssignedToWithDealId = async (eventId) => {
  const [{ Assigned_To__c: assignedTo }] = await salesforceConnMA
    .sobject('Event')
    .select('Assigned_To__c')
    .where(`Id='${eventId}'`);

  return assignedTo;
};

// Money Collection Payment Types live as custom objects in Salesforce
const getMoneyCollectionTypes = async () => {
  const moneyCollectionTypes = await salesforceConnMA
    .sobject('Money_Collection_Payment_Types__c')
    .select('Name, Payment_Code__c');
  return moneyCollectionTypes.reduce(
    (accum, curr) => ((accum[curr.Payment_Code__c] = curr.Name), accum),
    {}
  );
};

const getWxVisitsWithOpsId = async (opsId) => {
  const { records } = await salesforceConnMA.query(
    `SELECT Name, Scheduled_Date__c, First_Scheduled_By__c, Visit_Start_Date_Time__c FROM Wx_Visit__c WHERE Operations__c = '${opsId}' AND Scheduled_Date__c != NULL ORDER BY Scheduled_Date__c ASC`
  );
  return records;
};

const moneyCollectionErrorFileHandler = async (record, sfObject) => {
  let records = [];
  switch (sfObject) {
    case 'Operations__c':
      records = await getInfoFromAccountWithSiteId(record['CUSTOMER NUMBER']);
      records = records?.map((record) => {
        return {
          uniqueId: record.Operation__c,
          Contact_Last_Name__c: record.Contact_Last_Name__c,
          Customer_First_Name__c: record.Customer_First_Name__c,
        };
      });
      return records;
    case 'Opportunity':
      records = await getAccountInfoFromOppUsingAccSiteId(record['CUSTOMER NUMBER']);
      records = flattenRecords(records);
      return records;
    default:
      return records;
  }
};

// Util Function for moneyCollectionErrorFileHandler
const flattenRecords = (records) => {
  return records.map((record) => {
    const { Account, Unique_ID__c } = record;
    const { Contact_Last_Name__c, Customer_First_Name__c } = Account;
    return { uniqueId: Unique_ID__c, Contact_Last_Name__c, Customer_First_Name__c };
  });
};

const getInfoFromAccountWithSiteId = async (siteId) => {
  const { records } = await salesforceConnMA.query(`
    SELECT Contact_Last_Name__c, Customer_First_Name__c, Operation__c FROM Account WHERE Site_Id_NS__c = '${siteId}'
  `);
  return records || [];
};

// This query needs a extra WHERE param to find a specific record type or this will start returning a lot of Opportunities.
const getAccountInfoFromOppUsingAccSiteId = async (siteId) => {
  const { records } = await salesforceConnMA.query(`
    SELECT Account.Contact_Last_Name__c, Account.Customer_First_Name__c, Opportunity.Unique_ID__c FROM Opportunity WHERE Account.Site_Id_NS__c = '${siteId}'
  `);
  return records || [];
};

const getHVACSalesInfoWithAccountId = async (accountId) => {
  const { records: accountDetails } = await salesforceConnMA.query(`
  SELECT Id , Deal__c FROM Account WHERE Id = '${accountId}'
`);
  const { records: opportunityDetails } = await salesforceConnMA.query(`
  SELECT Unique_ID__c FROM Opportunity WHERE AccountId = '${accountId}'
`);

  const response = { accountId };

  if (accountDetails[0]) response.dealId = accountDetails[0]?.Deal__c || null;
  if (opportunityDetails[0]) response.opportunityId = opportunityDetails[0]?.Unique_ID__c || null;

  return response;
};

const processAccounts = async (data) => {
  const salesforceAxiosInstance = await getSalesforceAxiosInstance();
  const url = 'api/ProcessAccounts/';
  const response = await salesforceAxiosInstance({ url, data });
  return response.data;
};

const getDealIdFromAccount = async (accountId) => {
  const searchFields = { Id: accountId };
  const returnFields = ['Deal__c'];
  const result = await salesforceConnMA.sobject('Account').find(searchFields, returnFields);
  const deal = result[0];
  return deal?.Deal__c;
};

const getExistingDuplicateLeadsAndAccounts = async ({
  fieldsToReturn,
  searchTerms,
  objectType,
}) => {
  const searchPromises = [];
  /* eslint-disable no-loop-func */
  for (const key in searchTerms) {
    const value = searchTerms[key].replace(/[^\w\s@.]/g, '');

    console.log('search term', value);

    const search = async () => {
      try {
        const result = await salesforceConnMA.search(
          'FIND {' + value + '} IN ALL FIELDS RETURNING ' + fieldsToReturn
        );
        const { searchRecords } = result;
        for (const record of searchRecords) {
          record.matchedOn = key;
        }
        console.log('resolved', searchRecords);
        return searchRecords;
      } catch (err) {
        console.log('rejected', err);
        throw new Error(err);
      }
    };

    searchPromises.push(search());
  }

  console.log('searchPromises', searchPromises);

  /* eslint-enable no-loop-func */
  let data = await Promise.all(searchPromises);

  console.log('searchPromisesresults', data);

  data = [].concat(...data);
  if (objectType === 'Leads') {
    data = data.filter((record) => !record.IsConverted);
  }
  const recordsMap = new Map();

  for (const record of data) {
    if (recordsMap.has(record.Id)) {
      const existingRecord = recordsMap.get(record.Id);
      if (!existingRecord.matchedOn.includes(record.matchedOn[0])) {
        existingRecord.matchedOn.push(record.matchedOn[0]);
      }
    } else {
      recordsMap.set(record.Id, record);
    }
  }

  let records = Array.from(recordsMap.values());
  const dealIds = records.map((record) => record.Deal__c);
  const dealIdsString = dealIds.join("', '");
  if (objectType === 'Accounts') {
    const deals = await salesforceConnMA
      .sobject('Deal__c')
      .select('Id, Time_Stamp_HEA_Performed__c, HEA_Visit_Result__c')
      .where("Id in ('" + dealIdsString + "')")
      .execute();

    const twoYearsAgo = new Date();
    twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
    const keepDeals = deals.filter((deal) => {
      return (
        deal.HEA_Visit_Result__c !== 'HEA Performed' ||
        deal.Time_Stamp_HEA_Performed__c === null ||
        new Date(deal.Time_Stamp_HEA_Performed__c) > twoYearsAgo
      );
    });
    if (keepDeals.length !== records.length) {
      const keepRecords = keepDeals.map((d) => d.Id);
      records = records.filter((record) => keepRecords.includes(record.Deal__c));
    }
    for (const record of records) {
      const deal = deals.find((d) => d.Id === record.Deal__c);
      record.Time_Stamp_HEA_Performed__c =
        deal !== undefined && deal !== null && deal.Time_Stamp_HEA_Performed__c !== null
          ? deal.Time_Stamp_HEA_Performed__c
          : '';
    }
  }
  return records;
};

const updateLead = async (leadDetails) => {
  return await salesforceConnMA.sobject('Lead').update(leadDetails);
};

const convertLead = async (leadConvertObject) => {
  const convertedLead = await salesforceConnMA.soap.convertLead(leadConvertObject);
  return convertedLead;
};

const getPartnersEventInfoWithAccountIds = async (params) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();

    const url = 'getCustomerInfo/';

    const response = await salesforceAxiosInstance({ url, data: params });

    return JSON.parse(response.data);
  } catch (error) {
    console.log(error);
    console.log(error.response.data);
    error.params = { params };
    throw error;
  }
};

const getApprovalStageForCapCustomer = async (dealId) => {
  const [{ Approval_Stage__c: approvalStage }] = await salesforceConnMA
    .sobject('Deal__c')
    .select('Approval_Stage__c')
    .where(`Id='${dealId}'`);

  return approvalStage;
};

const getHESDashboardDetails = async (sfId, regionAbbreviation) => {
  const incorrectlyClosedWon = await getIncorrectlyClosedWonRecords({
    sfId,
    reportNameAbbr: `icw${regionAbbreviation || 'NS'}`,
  });
  const auditQCFail = await getAuditQCFail({
    sfId,
    reportNameAbbr: `auditQCFail${regionAbbreviation || 'NS'}`,
  });
  const unresultedVisits = await getUnresultedVisits({
    sfId,
    reportNameAbbr: `unresultedVisits${regionAbbreviation || 'NS'}`,
  });
  const dhwIcws = await getDhwIcw({
    sfId,
    reportNameAbbr: 'dhwIcw',
  });
  return { icws: { reports: { incorrectlyClosedWon, auditQCFail, unresultedVisits, dhwIcws } } };
};

const getCapHESDashboardDetails = async (sfId) => {
  const ieTeamICWReport = await getIETeamICWReport({
    sfId,
    reportNameAbbr: 'IETeamICWReportNew',
  });
  const ieTeamUnResultedVisit = await getIETeamUnResultedVisit({
    sfId,
    reportNameAbbr: 'IETeamUnresultedVisits',
  });
  const ampPendingReport = await getAMPPendingReport({
    sfId,
    reportNameAbbr: 'AMPPendingReport2.0',
  });
  const hvacIcwReport = await getHVACIcwReport({
    sfId,
    reportNameAbbr: 'HVACICWReport',
  });
  const capHVACFailedSalesQC = await getCAPHVACFailedSalesQC({
    sfId,
    reportNameAbbr: 'CAPHVACFailedSalesQC',
  });
  const capHVACReconcileReport = await getCAPHVACReconcileReportvReport({
    sfId,
    reportNameAbbr: 'CAPHVACReconcileReportv.2',
  });
  const ltaWaiverMissing = await getLTAWaiverMissing({
    sfId,
    reportNameAbbr: 'LTA/WaiverMissing',
  });
  const records = {
    incomeEligibles: {
      reports: {
        ieTeamICWReport,
        ieTeamUnResultedVisit,
        ampPendingReport,
        hvacIcwReport,
        capHVACFailedSalesQC,
        capHVACReconcileReport,
        ltaWaiverMissing,
      },
    },
  };

  return records;
};

const getIETeamICWReport = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const totalHesOriginalContractAmount = calculateAmount(records, 'hesOriginalContractAmount');
    const totalDaysSinceIcw = calculateAmount(records, 'daysSinceIcw');
    const ieTeamICWReport = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records.length,
        },
        totalHesOriginalContractAmount: {
          title: 'Total HES Original Contract Amount',
          value: `$${totalHesOriginalContractAmount.toFixed(2)}`,
        },
        totalDaysSinceIcw: {
          title: 'Total Days Since ICW',
          value: `$${totalDaysSinceIcw.toFixed(2)}`,
        },
      },
      title,
    };
    return ieTeamICWReport;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getIETeamUnResultedVisit = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const totalAmount = calculateAmount(records, 'hesOriginalContractAmount');
    const ieTeamUnResultedVisit = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records.length,
        },
        totalHesContractAmountIcw: {
          title: 'Total HES Original Contract Amount',
          value: `$${totalAmount.toFixed(2)}`,
        },
      },
      title,
    };
    return ieTeamUnResultedVisit;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getAMPPendingReport = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const totalAmount = calculateAmount(records, 'amp__Amount');
    const ampPendingReport = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records.length,
        },
        totalAmpAmount: {
          title: 'Total AMP Amount',
          value: `$${totalAmount.toFixed(2)}`,
        },
      },
      title,
    };
    return ampPendingReport;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getHVACIcwReport = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const totalAmount = calculateAmount(records, 'amount');
    const hvacIcwReport = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records.length,
        },
        totalAmount: {
          title: 'Total AMP Amount',
          value: `$${totalAmount.toFixed(2)}`,
        },
      },
      title,
    };
    return hvacIcwReport;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getCAPHVACFailedSalesQC = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const capVACFailedSalesQC = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records.length,
        },
      },
      title,
    };
    return capVACFailedSalesQC;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getCAPHVACReconcileReportvReport = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const ltaWaiverMissing = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records.length,
        },
      },
      title,
    };
    return ltaWaiverMissing;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getLTAWaiverMissing = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const totalRevisedContractAmount = calculateAmount(records, 'revisedContractAmount');
    const capHVACReconcileReport = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records.length,
        },
        totalRevisedContractAmount: {
          title: 'Total Revised Contract Amount',
          value: `$${totalRevisedContractAmount.toFixed(2)}`,
        },
      },
      title,
    };
    return capHVACReconcileReport;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getIncorrectlyClosedWonRecords = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const totalAmount = calculateAmount(records, 'hesOriginalContractAmount');
    const totalDays = calculateAmount(records, 'daysSinceIcw');
    const incorrectlyClosedWon = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records.length,
        },
        avgDaysSinceIcw: {
          title: 'Average Days Since ICW',
          value: Math.floor(totalDays / records.length),
        },
        totalHesContractAmountIcw: {
          title: 'Total HES Original Contract Amount',
          value: `$${totalAmount.toFixed(2)}`,
        },
      },
      title,
    };
    return incorrectlyClosedWon;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getUnresultedVisits = async (params) => {
  try {
    const response = await getReport(params);
    const { title } = response;
    let { records } = response;
    records = records.map((record) => {
      record.endDate = record.end;
      delete record.end;
      return record;
    });
    const unresultedVisits = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records?.length,
        },
      },
      title,
    };
    return unresultedVisits;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};
const getAuditQCFail = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const totalAmount = calculateAmount(records, 'heaInvoiceAmount');
    const auditQCFail = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records?.length,
        },
        totalHesContractAmountAuditQcFail: {
          title: 'Total HEA Invoice Amount',
          value: `$${totalAmount.toFixed(2)}`,
        },
      },
      title,
    };
    return auditQCFail;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getDhwIcw = async (params) => {
  try {
    const { title, records } = await getReport(params);
    const totalAmount = calculateAmount(records, 'hesDhwQuoteAmount');
    const dhwIcws = {
      records,
      metrics: {
        totalRecords: {
          title: 'Total Records',
          value: records?.length,
        },
        totalHesContractAmountDhwIcw: {
          title: 'Total HES DHW Quote Amount',
          value: `$${totalAmount.toFixed(2)}`,
        },
      },
      title,
    };
    return dhwIcws;
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getReport = async (params) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = 'getReportInfo/';
    const response = await salesforceAxiosInstance({ url, data: params });
    const title = response?.data?.name;
    const data = response?.data?.reportData?.[0];
    let recordsData = data?.recordsData || [];
    const { headerList, groupHeader } = response.data;
    const headerListDict = {};
    for (let i = 0; i < headerList.length; i++) {
      const header = headerList[i];
      const formattedHeader = header;
      headerListDict[
        formattedHeader
          .replace(':', '')
          .replace(/[^a-zA-Z0-9]/g, '_')
          .toLowerCase()
      ] = header;
    }
    if (data?.innerGroupingData) {
      const formattedResponse = getRecordsDataFromInnerGroupung(data.innerGroupingData);
      groupHeader.forEach((header) => {
        const formattedHeader = header;
        headerListDict[
          formattedHeader
            .replace(':', '')
            .replace(/[^a-zA-Z0-9]/g, '_')
            .toLowerCase()
        ] = header;
      });
      if (params.reportNameAbbr === 'dhwIcw') delete headerListDict.opportunity_owner_full_name;
      recordsData = [...recordsData, ...formattedResponse];
    }

    const records = [];
    const columnName = Object.keys(keysToCamelCase(headerListDict));
    recordsData.forEach(({ data }) => {
      const event = {};
      data.forEach((row, index) => {
        const isNameField = ['accountname', 'dealname', 'opportunityname', 'operationsname'].find(
          (key) => {
            return (
              columnName[index]?.toLowerCase()?.includes(key) &&
              !columnName[index].toLowerCase().includes('dealid')
            );
          }
        );
        const isDayPhone = ['dayphone', 'dayphone_2'].find((key) => {
          return columnName[index].toLowerCase().includes(key);
        });
        if (isNameField) event.name = row;
        else if (isDayPhone) {
          let value = row?.replace('</a>', '');
          value = value
            .slice(value?.indexOf('>') + 1, value?.length)
            ?.replace(/[^A-Z0-9]+/gi, '')
            ?.trim();
          event.phoneNumber = value;
        } else if (columnName[index].toLowerCase().includes('dealid')) event.dealId = row;
        else event[columnName[index]] = row === '-' ? row.replace('-', '') : row;
      });
      records.push(event);
    });

    return { title, records };
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getRecordsDataFromInnerGroupung = (innerGroupingData) => {
  const returnArray = [];
  innerGroupingData.forEach(({ recordsData, formulaAggregateResult, groupName }) => {
    const obj = { data: recordsData[0].data };
    if (formulaAggregateResult) obj.data = [...obj.data, ...formulaAggregateResult];
    obj.data = [...obj.data, groupName];
    returnArray.push(obj);
  });
  return returnArray;
};

const calculateAmount = (records, key) => {
  let amount = 0;
  for (let i = 0; i < records.length; i++) {
    const record = records[i];
    amount +=
      record[key].length <= 1 ? 0 : parseFloat(record[key].replace('$', '').replace(',', ''));
  }
  return amount;
};

const syncWorkReceiptFieldsToSF = async ({
  hvacInstallOpportunityId,
  sfBarrierObject,
  opportunityObject,
  accountId,
  needsOpportunityUpdate = false,
  needsContractUpdate = false,
  contractObject,
  accountsObject,
}) => {
  try {
    await upsertObject('Deal__c', sfBarrierObject);
    await upsertObject('Account', accountsObject);
    if (needsOpportunityUpdate) {
      if (hvacInstallOpportunityId) {
        opportunityObject.Id = hvacInstallOpportunityId;
        await upsertObject('Opportunity', opportunityObject);
      } else {
        const opportunityIds = await salesforceConnMA
          .sobject('Opportunity')
          .select('Id, Name')
          .where(`AccountId= '${accountId}'`);
        if (opportunityIds.length) {
          const [{ Id: hvacInstallOppId } = { Id: '' }] = opportunityIds
            .map(({ Id, Name }) => {
              if (Name.includes('HVAC Install')) return { Id, Name };
              return null;
            })
            .filter(Boolean);
          if (hvacInstallOppId) {
            opportunityObject.Id = hvacInstallOppId;
            await upsertObject('Opportunity', opportunityObject);
          }
        }
      }
    }
    if (needsContractUpdate) {
      await upsertObject('Contract', contractObject);
    }
    return sfBarrierObject;
  } catch (error) {
    console.log(error);
    error.params = { sfBarrierObject, opportunityObject };
    throw new Error(error);
  }
};

// TODO: update to fit naming conventions better. Not clear why there are both salesforce API names and _ case here.
// this should be normalized earlier in the process
const createBarrier = async (opportunityId, sfObject, values) => {
  let barrierTypes = [];
  const barrierTypeDict = {
    Bulk_Moisture__c: 'Bulk Moisture',
    disclosure_asbestos_in_work_area: 'Asbestos',
    disclosure_asbestos_wrapped_pipes: 'Asbestos',
    disclosure_asbestos_vermiculite: 'Vermiculite',
    disclosure_moisture_unvented_kitchen_fan: 'Kitchen Vent',
    Failed_CST_High_CO__c: 'Failed CST High CO',
    Failed_CST_Draft__c: 'Failed CST Draft',
    disclosure_cst_failed_co_gas_oven_level2: 'Failed CST Oven/Dryer',
    Inaccessible_Dirt_Crawlspace__c: 'Inaccessible Dirt Crawlspace',
    Failed_CST_Spillage__c: 'Failed CST Spillage',
    Mold__c: 'Mold',
    dislcosure_structural_saggy_roof: 'Roofing',
    Knob_and_Tube__c: 'K&T',
    Other_Roadblock__c: 'Other',
    Structural_Limitations__c: 'Structural Limitations',
  };

  Object.keys(barrierTypeDict).forEach((key) => {
    if (
      values[key] === 'Yes' ||
      values[key] === true ||
      sfObject[key] === true ||
      sfObject[key] === 'Yes'
    )
      barrierTypes.push(barrierTypeDict[key]);
  });
  barrierTypes = barrierTypes.filter((value, index, array) => {
    return array.indexOf(value) === index;
  });
  try {
    const environment = config.appEnv === 'production' ? config.appEnv : 'development';
    const url = `${config.lambda.createBarrierApiPath}/${environment}/create-barrier`;

    const { data } = await axios.post(url, {
      headers: {
        'Content-Type': 'application/json',
      },
      body: {
        types: barrierTypes,
        opportunityId,
      },
    });
    if (data.statusCode !== 200) {
      if (data.body.error) throw new Error(data.body.error);
      throw new Error(data.body);
    }
    return data;
  } catch (error) {
    error.params = { opportunityId, sfObject, values };
    throw new Error(error);
  }
};

const checkSiteIdDuplication = async (params) => {
  try {
    const salesforceAxiosInstance = await getSalesforceAxiosInstance();
    const url = 'api/checkDuplicateSiteId/';
    const response = await salesforceAxiosInstance({ url, data: params });
    return JSON.parse(response.data);
  } catch (error) {
    error.params = { params };
    throw error;
  }
};

const getOpportunityInfoWithAccountId = async (accountId) => {
  try {
    const response = await salesforceConnMA
      .sobject('Opportunity')
      .select('Id, Name')
      .where(`AccountId='${accountId}'`);
    return response;
  } catch (error) {
    return { error: "Couldn't find an Opportunity for that Account", accountId };
  }
};

module.exports = {
  convertLead,
  updateLead,
  getExistingDuplicateLeadsAndAccounts,
  upsertObject,
  createObject,
  getObject,
  updateObjects,
  updateObject,
  deleteObject,
  getAccountIdWithDealId,
  getAccountIdWithOperationsId,
  getContactInfoWithAccountId,
  getRecordTypeIdWithTypeName,
  getWxVisitInfo,
  updateSfResultingQuestions,
  returnResultStatusFromSf,
  updateMissingDocsStatus,
  getInsulationEventInfoWithOperationsId,
  getInsulationEventsInfoWithOperationsIds,
  getHEAEventInfoWithDealId,
  getWorkOrders,
  updateWorkOrder,
  hvacInstallOperation,
  swapEvents,
  handleMoneyCollection,
  getAssignedToWithDealId,
  getWxVisitsWithOpsId,
  getMoneyCollectionTypes,
  getSiteEvalDetails,
  hvacSiteEvalOperation,
  moneyCollectionErrorFileHandler,
  eaService,
  wxService,
  barrierService,
  hvacSalesOperation,
  getHVACSalesInfoWithAccountId,
  processAccounts,
  getDealIdFromAccount,
  getPartnersEventInfoWithAccountIds,
  getApprovalStageForCapCustomer,
  getHESDashboardDetails,
  getCapHESDashboardDetails,
  createBarrier,
  syncWorkReceiptFieldsToSF,
  checkSiteIdDuplication,
  getOpportunityInfoWithAccountId,
};
