let { salesforceConnection } = require('@config/salesforceConnect');
let { salesforceTwoConnection: salesforceTwoConn } = require('@config/newSalesforceConnect');

salesforceConnection.then((conn) => {
  salesforceConnection = conn;
});

salesforceTwoConn.then((conn) => {
  salesforceTwoConn = conn;
});

const getCTHEAEventInfo = async ({ objectName, sfId }) => {
  if (objectName === 'account') return getCTHEAEventInfoWithAccountId(sfId);
  if (objectName === 'workOrder') return getCTHEAEventInfoWithWorkOrderId(sfId);
  throw Error('No object ID or SF ID provided!');
};

const getCTHEAEventInfoWithAccountId = async (accountId, workVisitId) => {
  let returnObject = {};

  // ACCOUNT INFO
  const accountResponse = await salesforceTwoConn.query(`
    SELECT
      Gas_Project_Number__c,
      Electric_Project_Number__c,
      Lead_Source__c,
      Square_Footage__c,
      Year_Built__c,
      <PERSON>ing<PERSON>ddress,
      Email__c,
      Day_Phone__c,
      Market_Rate_Income_Eligible__c
    FROM Account
    WHERE Id='${accountId}'
  `);

  if (!accountResponse || !accountResponse?.records?.length)
    throw Error("Couldn't find a account page for that ID!");

  const [
    {
      Gas_Project_Number__c: gasProjectNumber,
      Electric_Project_Number__c: electricProjectNumber,
      Lead_Source__c: leadSource,
      Market_Rate_Income_Eligible__c: incomeEligibleOrMarketRate,
      Square_Footage__c: squareFeet,
      Year_Built__c: houseBuilt,
      Day_Phone__c: phoneNumber,
      Email__c: email,
      BillingAddress: billingAddress,
    },
  ] = accountResponse.records;

  const { city, postalCode, street } = billingAddress;

  const { customerName } = await getContactInfoWithAccountId(accountId);

  returnObject = {
    gasProjectNumber,
    electricProjectNumber,
    incomeEligibleOrMarketRate,
    leadSource,
    squareFeet,
    houseBuilt,
    customerName,
    phoneNumber,
    email,
    sfIds: { accountId },
    address: {
      displayAddress: `${street}, ${city}, CT, ${postalCode}`,
      street,
      city,
      postalCode,
      state: 'CT',
      country: null,
      geocodeAccuracy: null,
      latitude: null,
      longitude: null,
    },
  };
  // END ACCOUNT INFO

  // OPPORTUNITY INFO

  const opportunityResponse = await salesforceTwoConn.query(`
    SELECT 
        Id,
        Notes__c,
        Active_Barrier_Count__c,
        StageName
    FROM
        Opportunity
    WHERE
        AccountId='${accountId}'
    AND
      RecordType.Name='CT HEA'
  `);

  if (!opportunityResponse || !opportunityResponse?.records?.length) return returnObject;

  const activeOpportunity = opportunityResponse.records.find(({ StageName }) => {
    return !['Closed Lost', 'Dead'].includes(StageName);
  });

  if (!activeOpportunity) return returnObject;

  const {
    Id: opportunityId,
    Notes__c: officeNotes,
    Active_Barrier_Count__c: numberOfActiveBarriers,
  } = activeOpportunity;

  returnObject.sfIds.opportunityId = opportunityId;

  // END OPPORTUNITY INFO

  // WORK VISIT INFO
  const workVisitResponse = await salesforceTwoConn.query(`
    SELECT
        Id,
        Visit_Status__c,
        Confirmation_Status__c,
        Notes__c,
        Visit_Notes__c,
        Blower_Door_Pre__c,
        Duct_Sealing_Opp_Linear_Feet__c,
        HVAC_Result_Detail__c,
        Window_Result_Detail__c,
        Wx_Result_Detail__c,
        Work_Order__r.Id,
        Arrival_Window__c
    FROM
        Work_Visit__c
    WHERE
        Account__c='${accountId}'
    `);

  // If they are first scheduling a visit there will be no work visits yet
  if (!workVisitResponse || !workVisitResponse?.records?.length) return returnObject;

  const activeWorkVisit = workVisitResponse.records.find(({ Visit_Status__c, Id }) => {
    return workVisitId
      ? Id === workVisitId
      : !['Canceled', 'Walk', 'Needs to be rescheduled'].includes(Visit_Status__c);
  });

  // If they're rescheduling a previously canceled visit, there will be a canceled visit that is not valid
  if (!activeWorkVisit) return returnObject;

  const {
    Confirmation_Status__c: heaConfirmationStatus,
    Visit_Notes__c: fieldNotes,
    Work_Order__r: { Id: workOrderId } = {},
    Blower_Door_Pre__c: initialBlowerDoorReading,
    Duct_Sealing_Opp_Linear_Feet__c: ductSealingOpp,
    HVAC_Result_Detail__c: hvacResultDetail,
    Window_Result_Detail__c: windowResultDetail,
    Wx_Result_Detail__c: wxResultDetail,
    Arrival_Window__c: arrivalWindow,
  } = activeWorkVisit;

  const workVisitInfo = {
    // TODO: this field is a duplicate on the event table. we already have a confirmation_status field
    heaConfirmationStatus,
    initialBlowerDoorReading,
    ductSealingOpp,
    numberOfActiveBarriers,
    hvacResultDetail,
    windowResultDetail,
    wxResultDetail,
    arrivalWindow,
    notes: { fieldNotes, officeNotes },
  };

  returnObject.sfIds.workOrderId = workOrderId;

  returnObject = {
    ...returnObject,
    ...workVisitInfo,
  };
  // END WORK VISIT INFO

  return returnObject;
};

const getCTHEAEventInfoWithWorkOrderId = async (workOrderId) => {
  const response = await salesforceTwoConn
    .sobject('Work_Order__c')
    .select(
      `
      Account__c
    `
    )
    .where(`Id='${workOrderId}'`);

  if (!response.length) throw Error("Couldn't find a work order for that ID!");

  const [{ Account__c: accountId }] = response;

  return getCTHEAEventInfoWithAccountId(accountId);
};

const getContactInfoWithAccountId = async (accountId) => {
  const response = await salesforceTwoConn
    .sobject('Contact')
    .select('Name')
    .where(`AccountId='${accountId}'`);
  if (!response.length) throw Error("Couldn't find a contact page for that ID!");
  const [{ Name }] = response;
  return { customerName: Name };
};

const getExistingDuplicateLeadsAndAccounts = async ({
  fieldsToReturn,
  searchTerms,
  objectType,
}) => {
  const searchPromises = [];
  /* eslint-disable no-loop-func */
  for (const key in searchTerms) {
    const value = searchTerms[key].replace(/[^\w\s@.]/g, '');
    searchPromises.push(
      new Promise(function(resolve, reject) {
        salesforceTwoConn.search(
          'FIND {' + value + '} IN ALL FIELDS RETURNING ' + fieldsToReturn,
          (err, records) => {
            if (err) {
              reject(err);
            } else {
              records = records.searchRecords;
              for (const record of records) {
                record.matchedOn = [key];
              }
              resolve(records);
            }
          }
        );
      })
    );
  }
  /* eslint-enable no-loop-func */
  let data = await Promise.all(searchPromises);
  data = [].concat(...data);
  if (objectType === 'Leads') {
    data = data.filter((record) => !record.IsConverted);
  }
  const recordsMap = new Map();

  for (const record of data) {
    if (recordsMap.has(record.Id)) {
      const existingRecord = recordsMap.get(record.Id);
      if (!existingRecord.matchedOn.includes(record.matchedOn[0])) {
        existingRecord.matchedOn.push(record.matchedOn[0]);
      }
    } else {
      recordsMap.set(record.Id, record);
    }
  }
  const records = Array.from(recordsMap.values());
  return records;
};

module.exports = {
  getCTHEAEventInfo,
  getCTHEAEventInfoWithAccountId,
  getExistingDuplicateLeadsAndAccounts,
};
