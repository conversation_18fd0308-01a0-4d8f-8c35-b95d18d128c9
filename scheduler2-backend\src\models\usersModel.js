const moment = require('moment');
const utilityService = require('@homeworksenergy/utility-service');
const db = require('@config/databaseConnect');
const { keysToCamelCase, keysToUnderscoreCase, removeEmptiesFromObject } = require('@utils');
const { DEVOPS_SF_ID } = require('@utils/constants');

const addressModel = require('@models/addressModel');

// Simple in-memory cache for user roles (5 minute TTL)
const userRolesCache = new Map();
const USER_ROLES_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

const getCachedUserRoles = (oid) => {
  const cached = userRolesCache.get(oid);
  if (cached && Date.now() - cached.timestamp < USER_ROLES_CACHE_TTL) {
    return cached.data;
  }
  return null;
};

const setCachedUserRoles = (oid, data) => {
  userRolesCache.set(oid, {
    data,
    timestamp: Date.now()
  });

  // Clean up old cache entries periodically
  if (userRolesCache.size > 1000) {
    const now = Date.now();
    for (const [key, value] of userRolesCache.entries()) {
      if (now - value.timestamp > USER_ROLES_CACHE_TTL) {
        userRolesCache.delete(key);
      }
    }
  }
};

const clearUserRolesCache = (oid) => {
  if (oid) {
    userRolesCache.delete(oid);
  } else {
    userRolesCache.clear();
  }
};

const getUsersWithOids = async (oids) => {
  try {
    const { rows: users } = await db.raw(
      `
      SELECT
      u.*,
      u.company AS company_key,
      c.name AS company_name,
      re.name AS region_name,
      re.abbreviation AS region_abbreviation,
      (
        SELECT
          CASE 
          WHEN a.address IS NOT NULL THEN 
            json_build_object(
              'address', a.address,
              'display_address', CONCAT(a.street, ', ', a.city, ', ', a.state, ' ', a.postal_code),
              'street', a.street,
              'city', a.city,
              'state', a.state,
              'postal_code', a.postal_code,
              'latitude', a.latitude,
              'longitude', a.longitude
            )
          ELSE null
          END 
        FROM
          addresses a
        LEFT JOIN
          user_addresses uad
          ON a.address = uad.address
          AND u.oid = uad.oid
        WHERE
          uad.type = 'dayStart'
      ) AS agent_address,
      ARRAY(
          SELECT
              a.attribute_name
          FROM
              user_attributes ua
                  JOIN
              attributes a
                  ON ua.attribute_id = a.id
          WHERE
              ua.oid = u.oid
      ) as attribute_names,
      ARRAY(
          SELECT
              ua.attribute_id
          FROM
              user_attributes ua
          WHERE
              ua.oid = u.oid
      ) as attributes,
      ARRAY(
          SELECT
              et.name
          FROM
              users_eventtypes uet
                  JOIN
              event_types et
                  ON uet.type_id = et.id
          WHERE
              uet.oid = u.oid
      ) as event_type_names,
      ARRAY(
          SELECT
              et.event_type
          FROM
              event_types et
                  JOIN
              users_eventtypes uet
                  ON uet.type_id = et.id
          WHERE
              uet.oid = u.oid
      ) AS event_types,
      ARRAY(
        SELECT
          up.program
        FROM
          user_programs up
        WHERE
          up.oid = u.oid
      ) AS user_programs
  FROM
      users u
          LEFT OUTER JOIN
      companies c
          ON u.company = c.key
          LEFT OUTER JOIN
      regions re
          ON u.region = re.region_id
  WHERE
      u.oid = ANY(?)
  ORDER BY u.region ASC
  `,
      [oids]
    );
    return keysToCamelCase(users);
  } catch (error) {
    error.params = { oids };
    throw error;
  }
};

const getUserWithOid = async (oid) => {
  const [user] = await getUsersWithOids([oid]);
  return user;
};

const initializeUser = async (params) => {
  const { given_name: firstName, family_name: lastName, upn: email, oid } = params;
  try {
    const selectResponse = await db.raw('SELECT * FROM users WHERE oid = ?', oid);
    if (selectResponse.rows.length) {
      const user = selectResponse.rows[0];
      global.logger.info('User is in the system: %o', user);
      return { user };
    }
    global.logger.info('Initializing user %o', oid);

    const response = await db.raw(
      'INSERT INTO users (oid, firstname, lastname, display_name, email) VALUES (?, ?, ?, ?, ?) RETURNING *',
      [oid, firstName, lastName, `${firstName} ${lastName}`, email]
    );
    return { user: response.rows[0] };
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const createUser = async (params) => {
  const {
    oid,
    firstName,
    lastName,
    phoneNumber,
    home,
    region,
    program,
    sfId,
    email,
    state,
    company,
    department,
    number,
    crewLead,
    manager,
  } = params;
  try {
    await db.raw(
      `
INSERT INTO users (
  oid,
  firstname,
  lastname,
  phone_number,
  home,
  region,
  program,
  sf_id,
  email,
  state,
  company,
  department,
  number,
  crew_lead,
  manager
) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`,
      [
        oid,
        firstName,
        lastName,
        phoneNumber,
        home,
        region,
        program,
        sfId,
        email,
        state,
        company,
        department,
        number,
        crewLead,
        manager,
      ]
    );
    return { message: 'Successfully inserted new user' };
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const getNameWithOid = async (oid) => {
  try {
    const { rows } = await db.raw(
      `SELECT u.firstname, u.lastname, u.type, c.name AS company_name  
      FROM users u 
      JOIN companies c 
      ON u.company = c.key 
      WHERE oid = ?`,
      [oid]
    );

    const [user] = keysToCamelCase(rows);

    return `${user.firstname} ${user.lastname}`;
  } catch (error) {
    error.params = { oid };
    throw error;
  }
};

// Returning DEVOPS_SF_ID so we can find these events in Salesforce and correct the issue
// User is missing sf_id. Need to assign one to this user
const getSalesforceIdWithOid = async (oid) => {
  if (!oid) return DEVOPS_SF_ID;
  try {
    const response = await db.raw('SELECT sf_id FROM users WHERE oid = (?)', [oid]);
    return { userSfId: response.rows[0].sf_id || DEVOPS_SF_ID };
  } catch (error) {
    error.params = oid;
    throw error;
  }
};

const getUsersForEventType = async (type) => {
  // If generic HVAC Install event, make eventtype 0004% to grab all hvac install events
  if (type === '000400') type = `${type.slice(0, 4)}%`;
  try {
    const response = await db.raw(
      `SELECT 
      DISTINCT 
        users.*, 
        companies.name AS company_name,
      ARRAY(
        SELECT
            a.id
        FROM
            user_attributes ua
                JOIN
            attributes a
                ON ua.attribute_id = a.id
        WHERE
            ua.oid = users.oid
      ) as attribute_id,
      ARRAY(
        SELECT
          up.program
        FROM
          user_programs up
        WHERE
          up.oid = users.oid
      ) AS user_programs
      FROM users
      JOIN companies 
        ON companies.key = users.company
      JOIN users_eventtypes 
        ON users_eventtypes.oid = users.oid
      JOIN event_types 
        ON event_types.id = users_eventtypes.type_id
      WHERE event_types.event_type LIKE ?
        AND users.active = true`,
      [type]
    );
    return { users: keysToCamelCase(response.rows) };
  } catch (error) {
    error.params = { type };
    throw error;
  }
};

const getAgentOidsForDepartments = async (departmentNames) => {
  const namesArray = Array.isArray(departmentNames) ? departmentNames : [departmentNames];
  // TODO: synchronize database department names with the utility service
  const formattedDepartmentNames = namesArray.map((name) => name.replace(' ', '-'));
  // If generic HVAC Install event, make eventtype 0004% to grab all hvac install events

  try {
    const response = await db.raw(
      `SELECT 
      DISTINCT
        u.oid
      FROM users u
      JOIN user_roles ur
        ON ur.oid = u.oid
      JOIN roles r
        ON r.id = ur.role_id
      JOIN departments d
        ON d.id = ur.department_id
      WHERE d.department = ANY(?)
        AND r.role ILIKE 'agent' -- Only get users that should appear on this schedule`,
      [formattedDepartmentNames]
    );

    return response.rows.map((row) => row.oid);
  } catch (error) {
    error.params = { departmentNames };
    throw error;
  }
};

const getAgentOidsForDepartmentsAndState = async (params) => {
  let whereClause = [];
  Object.keys(params).forEach((stateAbbr) => {
    const departments = params[stateAbbr];
    const arr = departments.map((department) => {
      const formattedDepartmentNames = department.replace(' ', '-');
      return `(d.department = '${formattedDepartmentNames}' AND s.abbreviation = '${stateAbbr}' AND r.role ILIKE 'agent')`;
    });
    whereClause = [...whereClause, ...arr];
  });

  try {
    const response = await db.raw(
      `SELECT 
      DISTINCT
        u.oid
      FROM users u
      JOIN user_roles ur
        ON ur.oid = u.oid
      JOIN roles r
        ON r.id = ur.role_id
      JOIN departments d
        ON d.id = ur.department_id
      JOIN states s
        ON s.event_type = ur.state
      WHERE 
      ${whereClause.join('OR')}`
    );

    return response.rows.map((row) => row.oid);
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const getWalkthroughTech = async (contractId) => {
  /*Gets Walkthrough Tech given an HVAC contract ID */
  try {
    const {
      rows: [tech],
    } = await db.raw(
      `SELECT u.* 
      FROM event e 
      JOIN users u 
        ON e.oid=u.oid 
      WHERE e.type='000300' and 
      e.sf_ids->>'deal_id' LIKE 
        (SELECT SUBSTRING(sf_ids->>'deal_id', -3) || '%'
        FROM event 
        WHERE sf_ids->>'contract_id' = ? LIMIT 1);`,
      contractId
    );
    if (!tech) throw Error("Couldn't find the walkthrough tech for this job!");
    return keysToCamelCase({ tech });
  } catch (error) {
    error.params = { contractId };
    throw error;
  }
};

const updateUser = async (params, trx) => {
  const { oid } = params;

  if (Object.keys(params).length > 0) {
    try {
      const insertFields = keysToUnderscoreCase(removeEmptiesFromObject(params));

      // TODO: create util function to do this logic, it's used in at least 3 places
      const [setClause, wildcardArray] = Object.keys(insertFields).reduce(
        (acc, key, index, array) => {
          // eslint-disable-next-line prefer-const
          let [setClause, wildcardArray] = acc;
          const val = insertFields[key];

          setClause = `${setClause} ?? = ?`;
          wildcardArray.push(key, val);

          // Don't add comma on last entry to set clause
          if (index < array.length - 1) setClause = `${setClause},`;

          return [setClause, wildcardArray];
        },
        ['SET', []]
      );
      const {
        rows: [updatedUser],
      } = await db
        .raw(`UPDATE users ${setClause} WHERE oid = ? RETURNING *`, [...wildcardArray, oid])
        .transacting(trx);
      return updatedUser;
    } catch (error) {
      error.params = params;
      throw error;
    }
  }
};

const updateUserAttributes = async (oid, attributes, trx) => {
  try {
    await db
      .raw(
        `DELETE FROM user_attributes 
        WHERE oid = (?)`,
        [oid]
      )
      .transacting(trx);

    if (attributes.length > 0) {
      const insertWildcardArray = attributes.reduce((acc, curr) => {
        acc.push(oid);
        acc.push(curr);
        return acc;
      }, []);

      await db
        .raw(
          `INSERT INTO user_attributes (oid, attribute_id) VALUES ${attributes
            .map(() => '(?, ?)')
            .join(',')} RETURNING *`,
          insertWildcardArray
        )
        .transacting(trx);
    }
  } catch (error) {
    error.params = { oid, attributes };
    throw error;
  }
};

const updateUserPrograms = async (oid, programs, trx) => {
  try {
    await db
      .raw(
        `DELETE FROM user_programs 
        WHERE oid = (?)`,
        [oid]
      )
      .transacting(trx);

    if (programs.length > 0) {
      const insertWildcardArray = programs.reduce((acc, curr) => {
        acc.push(oid);
        acc.push(curr);
        return acc;
      }, []);

      await db
        .raw(
          `INSERT INTO user_programs (oid, program) VALUES ${programs
            .map(() => '(?, ?)')
            .join(',')} RETURNING *`,
          insertWildcardArray
        )
        .transacting(trx);
    }
  } catch (error) {
    error.params = { oid, programs };
    throw error;
  }
};

const getUserEventTypes = async (oid) => {
  try {
    const response = await db.raw('SELECT type_id FROM users_eventtypes WHERE oid = ?', [oid]);
    const userEventTypes = response.rows.map((type) => type.type_id);
    return {
      userEventTypes,
    };
  } catch (error) {
    error.params = { oid };
    throw error;
  }
};

const addEventType = async (params, trx) => {
  const { eventType, oid } = params;
  try {
    await db
      .raw(
        `INSERT INTO users_eventtypes 
          (
            oid, 
            type_id
          ) 
        VALUES 
          (
            ?, 
            (
              SELECT 
                id  -- type_id is not necessarily the same between prod and dev right now. Since the event types are unique, we don't really need a type_id anyway. I would eventually like to remove the id field from the event_types table
              FROM 
                event_types 
              WHERE 
                event_type = ?
            )
          )`,
        [oid, eventType]
      )
      .transacting(trx);
  } catch (error) {
    error.params = params;
    throw error;
  }
};

const removeEventTypes = async (oid, trx) => {
  try {
    return db.raw('DELETE FROM users_eventtypes WHERE oid = ?', [oid]).transacting(trx);
  } catch (error) {
    error.params = { oid };
    throw error;
  }
};

const getAllUsers = async () => {
  try {
    const { rows } = await db.raw(
      `SELECT 
        u.*,
        ARRAY (
          SELECT 
          DISTINCT
            d.department
          FROM 
            departments d 
          JOIN 
            user_roles ur
            ON
            ur.department_id = d.id
          WHERE 
            u.oid = ur.oid
        ) AS departments
      FROM 
        users u
      WHERE 
        u.type='user' 
          AND 
        u.active = true
      ORDER BY u.lastname
        `
    );
    return keysToCamelCase(rows);
  } catch (error) {
    error.params = 'no params';
    throw error;
  }
};

const getUserLoginInfo = async (oid) => {
  if (!oid) {
    throw new Error('OID is required for getUserLoginInfo');
  }

  try {
    const result = await Promise.race([
      db.raw(
        `SELECT
          company,
          is_manager,
          department,
          state
        FROM
          users
        WHERE
          oid = ?`,
        [oid]
      ),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('getUserLoginInfo query timeout')), 10000)
      )
    ]);

    const { rows: [userInfo] } = result;

    if (!userInfo) {
      return { company: '', isManager: false, department: '', state: '', stateAbbr: '' };
    }

    return keysToCamelCase(userInfo);
  } catch (error) {
    global.logger?.error('getUserLoginInfo failed for oid %s: %o', oid, error.message);
    error.params = oid;

    if (error.message.includes('timeout')) {
      error.message = `Database query timeout for user login info (oid: ${oid})`;
    }

    throw error;
  }
};

const getUserRoles = async (oid) => {
  if (!oid) {
    throw new Error('OID is required for getUserRoles');
  }

  // Check cache first
  const cachedRoles = getCachedUserRoles(oid);
  if (cachedRoles) {
    global.logger?.debug('Returning cached user roles for oid: %s', oid);
    return cachedRoles;
  }

  try {
    // Use a timeout for this query to prevent hanging connections
    const result = await Promise.race([
      db.raw(
        `SELECT
        DISTINCT
          r.role,
          r.id AS role_id,
          ur.state,
          d.department,
          d.id AS department_id,
          s.name AS state_fullname,
          s.abbreviation AS state_abbr
        FROM user_roles AS ur
        JOIN departments AS d
          ON ur.department_id = d.id
        JOIN roles AS r
          ON ur.role_id = r.id
        JOIN states as s
          ON ur.state = s.event_type
        WHERE
          ur.oid = ?
        ORDER BY role_id ASC`,
        [oid]
      ),
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('getUserRoles query timeout')), 15000)
      )
    ]);

    const { rows } = result;
    const userRoles = keysToCamelCase(rows || []);

    // Cache the result
    setCachedUserRoles(oid, userRoles);

    return userRoles;
  } catch (error) {
    global.logger?.error('getUserRoles failed for oid %s: %o', oid, error.message);
    error.params = { oid };

    // If it's a timeout error, provide a more specific message
    if (error.message.includes('timeout')) {
      error.message = `Database query timeout for user roles (oid: ${oid})`;
    }

    throw error;
  }
};

const deleteUserRoles = async (oid, departmentId, state) => {
  try {
    const result = await db.raw(
      'DELETE FROM user_roles WHERE oid = (?) AND department_id = (?) AND state = (?)',
      [oid, departmentId, state]
    );

    // Clear cache for this user
    clearUserRolesCache(oid);

    return result;
  } catch (error) {
    error.params = { oid };
    throw error;
  }
};
const updateUserRoles = async (oid, roleId, departmentId, state) => {
  try {
    const { stateCode } = utilityService.getHweCode({ state });
    const result = await db.raw(
      'UPDATE user_roles SET role_id = (?), state = (?) WHERE oid = (?) AND department_id = (?) AND state = (?)',
      [roleId, stateCode, oid, departmentId, stateCode]
    );

    // Clear cache for this user
    clearUserRolesCache(oid);

    return result;
  } catch (error) {
    error.params = { oid };
    throw error;
  }
};

const insertUserRole = async (roleId, oid, departmentId, state) => {
  try {
    const { stateCode } = utilityService.getHweCode({ state });
    const result = await db.raw('INSERT INTO user_roles(role_id, oid,department_id, state) VALUES(?, ?, ?, ?)', [
      roleId,
      oid,
      departmentId,
      stateCode,
    ]);

    // Clear cache for this user
    clearUserRolesCache(oid);

    return result;
  } catch (error) {
    error.params = { roleId, oid };
    throw error;
  }
};

const deactivateUser = async (userOid) => {
  try {
    await db.raw('UPDATE users SET active = false WHERE oid = ?', [userOid]);
    return { message: 'success' };
  } catch (error) {
    error.params = { oid: userOid };
    throw error;
  }
};

const getOverviewForUsers = async (oids, startDate, endDate) => {
  // Also accept singular oid
  if (!Array.isArray(oids)) oids = [oids];
  const {
    rows,
  } = await db.raw('SELECT * FROM OVERVIEW WHERE OID = ANY(?) AND date >= ? and date <= ?', [
    oids,
    startDate,
    endDate,
  ]);
  const overviews = keysToCamelCase(rows);

  // Create map of oids to return overviews for
  const returnRows = {};

  const dateFormat = 'YYYY-MM-DD';
  let startMoment = moment(startDate);

  // Loop through each requested oid
  for (let i = 0; i < oids.length; i++) {
    const oid = oids[i];

    // Initialize return info for user oid
    if (!returnRows[oid]) returnRows[oid] = {};

    // Loop through days to determine open/closed status for each date in the search
    // (closed days have no overview entry)
    while (startMoment.isSameOrBefore(endDate)) {
      const formattedDate = startMoment.format(dateFormat);

      // Search the db results for overview for current user, current date
      const overviewIndex = rows.findIndex((row) => {
        if (row.oid !== oid) return false;
        return moment(row.date).format(dateFormat) === formattedDate;
      });

      // if exists, date is open
      if (overviewIndex > -1) {
        const overviewInfo = overviews[overviewIndex];
        const { maxAppt, dayStart, dayEnd } = overviewInfo;
        // set status open and take maxappt, daystart, dayend
        returnRows[oid][formattedDate] = { status: 'open', maxAppt, dayStart, dayEnd, events: [] };
      } else {
        // else set status closed
        returnRows[oid][formattedDate] = { status: 'closed', events: [] };
      }

      startMoment.add(1, 'days');
    }

    startMoment = moment(startDate);
  }

  return returnRows;
};

const getUserSfIdWithOid = async (oid) => {
  try {
    const {
      rows: [sfIdResponse],
    } = await db.raw('SELECT sf_id FROM users WHERE oid = ?', [oid]);
    if (!sfIdResponse) return false;
    const { sf_id: sfId } = sfIdResponse;
    return sfId;
  } catch (error) {
    error.params = { oid };
    throw error;
  }
};

const updateUserAddress = async (oid, addressComponents, type, trx, getAddressInfo = true) => {
  let address = addressComponents?.address;
  if (getAddressInfo) {
    address = await addressModel.insertAddress(addressComponents, trx);
  }

  const {
    rows: [exisitingAddress],
  } = await db
    .raw('SELECT * FROM user_addresses WHERE oid = ? AND type = ?', [oid, type])
    .transacting(trx);

  if (exisitingAddress) {
    await db
      .raw('UPDATE user_addresses SET address = ? WHERE oid = ? AND type = ?', [address, oid, type])
      .transacting(trx);
  } else {
    await db
      .raw('INSERT INTO user_addresses(address, oid, type) VALUES(?, ?, ?)', [address, oid, type])
      .transacting(trx);
  }

  return true;
};

const getUserLastUpdatedSwapMonth = async ({ oid }) => {
  try {
    const { rows } = await db.raw(`SELECT last_updated_swap_month FROM users WHERE oid = '${oid}'`);
    return keysToCamelCase(rows[0]);
  } catch (error) {
    throw new Error(error);
  }
};

const setUserLastUpdatedSwapMonth = async ({ oid, date }) => {
  try {
    const { rows } = await db.raw(
      `UPDATE users SET last_updated_swap_month = '${date}' WHERE oid = '${oid}'`
    );
    return keysToCamelCase(rows[0]);
  } catch (error) {
    throw new Error(error);
  }
};

const getAgentDepartmentAndRegionalOfficeWithOid = async (oid) => {
  const { rows } = await db.raw(
    `
    SELECT
      oa.address AS officeAddress
    FROM
      users u
    LEFT JOIN 
      office_addresses oa
      ON u.region = oa.region
    WHERE
      u.oid = ?
  `,
    [oid]
  );
  return keysToCamelCase(rows[0]);
};

const getUsersOfCrewType = async (departmentId = 6) => {
  try {
    const { rows } = await db.raw(
      `SELECT 
        u.display_name,
        u.oid
      FROM 
        users u
      JOIN
        user_roles ur
        ON u.oid = ur.oid
      WHERE 
        u.type='crew' 
          AND
        u.active = true
          AND
        ur.department_id = ?
          AND
        ur.role_id = 6
      ORDER BY u.display_name
        `,
      [departmentId]
    );
    return keysToCamelCase(rows);
  } catch (error) {
    error.params = 'Failed to get crews...';
    throw error;
  }
};

const getCrewByDisplayName = async (displayName) => {
  try {
    const { rows } = await db.raw(
      `SELECT 
        u.oid
      FROM 
        users u
      WHERE 
        u.display_name = ? 
        `,
      [displayName]
    );
    return keysToCamelCase(rows);
  } catch (error) {
    error.params = 'Failed to get display name...';
    throw error;
  }
};

module.exports = {
  getUsersWithOids,
  getUserWithOid,
  initializeUser,
  createUser,
  getNameWithOid,
  getSalesforceIdWithOid,
  getUsersForEventType,
  getUserLastUpdatedSwapMonth,
  setUserLastUpdatedSwapMonth,
  getAgentOidsForDepartments,
  getAgentOidsForDepartmentsAndState,
  updateUser,
  getUserEventTypes,
  addEventType,
  removeEventTypes,
  getAllUsers,
  getUserLoginInfo,
  deleteUserRoles,
  updateUserRoles,
  insertUserRole,
  getOverviewForUsers,
  getWalkthroughTech,
  deactivateUser,
  getUserRoles,
  updateUserAttributes,
  getUserSfIdWithOid,
  updateUserAddress,
  updateUserPrograms,
  getAgentDepartmentAndRegionalOfficeWithOid,
  getUsersOfCrewType,
  getCrewByDisplayName,
  clearUserRolesCache,
};
