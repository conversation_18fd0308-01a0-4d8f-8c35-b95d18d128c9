const {
  LocationClient,
  CalculateRouteCommand,
  SearchPlaceIndexForTextCommand,
} = require('@aws-sdk/client-location');
const config = require('@config/config');

const location = new LocationClient({
  region: config.awsRegion,

  credentials: {
    accessKeyId: config.awsAccessKeyId,
    secretAccessKey: config.awsAccessSecretKey,
  },
});

const getRouteInfo = async ({
  originGeolocation,
  destinationGeolocation,
  waypointsGeolocations,
}) => {
  console.log('HERE!!!');
  const location = new LocationClient({
    credentials: {
      accessKeyId: config.awsAccessKeyId,
      secretAccessKey: config.awsAccessSecretKey,
    },
  });

  const params = {
    CalculatorName: 'SchedulerRouteCalculator',
    DeparturePosition: originGeolocation, // [-71.082, 42.4017] Office in medford, takes [long, lat] array
    DestinationPosition: destinationGeolocation, // [-71.148, 42.5096] Office in woburn
    WaypointPositions: waypointsGeolocations,
    // TODO: set this up with the visit start time
    // DepartureTime: new Date() || 'Wed Dec 31 1969 16:00:00 GMT-0800 (PST)' || *********,
    DepartNow: true,
    // TODO: remove DepartNow once departure time is set up
    DistanceUnit: 'Miles',
    TravelMode: 'Car',
    // TODO: not sure if this is necessary
    IncludeLegGeometry: true,
  };

  console.log('Calculating route with params:', params);

  const route = await location.send(new CalculateRouteCommand(params));

  console.log('Route calculated:', route);
  return parseRoute(route);
};

const parseRoute = (route) => {
  const { Legs: legs } = route;

  // Should only ever be 2 legs for drivetime calc, but can work with more
  return legs.map((leg) => {
    const { Distance: distance, DurationSeconds: durationSeconds } = leg;

    const mileage = parseFloat(distance.toFixed(2));
    // TODO: do we care to store any time more specific than the minute?
    const driveTime = Math.floor(durationSeconds / 60);

    return { driveTime, mileage };
  });
};

const getGeolocation = async (address) => {
  const response = await location.send(
    new SearchPlaceIndexForTextCommand({
      IndexName: 'SchedulerPlaceIndex',
      Text: address,
      FilterCountries: ['USA'],
    })
  );

  // TODO: do we need to think about results other than the first one?
  const geolocation = response?.Results?.[0]?.Place?.Geometry?.Point;

  return geolocation;
};
module.exports = { getRouteInfo, getGeolocation };
