const db = require('@config/databaseConnect');

// Database monitoring utilities
class DatabaseMonitor {
  constructor() {
    this.queryCount = 0;
    this.errorCount = 0;
    this.slowQueries = [];
    this.startTime = Date.now();
  }

  // Log query statistics
  logQueryStats() {
    const uptime = Date.now() - this.startTime;
    const avgQueriesPerSecond = this.queryCount / (uptime / 1000);
    
    global.logger?.info('Database Stats: %o', {
      totalQueries: this.queryCount,
      totalErrors: this.errorCount,
      avgQueriesPerSecond: avgQueriesPerSecond.toFixed(2),
      uptimeMs: uptime,
      slowQueriesCount: this.slowQueries.length
    });

    // Log slow queries if any
    if (this.slowQueries.length > 0) {
      global.logger?.warn('Slow queries detected: %o', this.slowQueries.slice(-5));
    }
  }

  // Track slow queries
  trackSlowQuery(sql, duration, bindings) {
    this.slowQueries.push({
      sql: sql.substring(0, 200) + (sql.length > 200 ? '...' : ''),
      duration,
      bindings: bindings ? bindings.slice(0, 5) : [],
      timestamp: new Date().toISOString()
    });

    // Keep only last 50 slow queries
    if (this.slowQueries.length > 50) {
      this.slowQueries = this.slowQueries.slice(-50);
    }
  }

  // Check database connection health
  async checkHealth() {
    try {
      const start = Date.now();
      await db.raw('SELECT 1 as health_check');
      const duration = Date.now() - start;
      
      global.logger?.debug('Database health check passed in %dms', duration);
      
      if (duration > 1000) {
        global.logger?.warn('Database health check slow: %dms', duration);
      }
      
      return { healthy: true, responseTime: duration };
    } catch (error) {
      global.logger?.error('Database health check failed: %o', error.message);
      return { healthy: false, error: error.message };
    }
  }

  // Get connection pool information
  getPoolInfo() {
    try {
      const client = db.client;
      if (client && client.pool) {
        return {
          used: client.pool.numUsed?.() || 'unknown',
          free: client.pool.numFree?.() || 'unknown',
          pending: client.pool.numPendingAcquires?.() || 'unknown',
          pendingCreates: client.pool.numPendingCreates?.() || 'unknown'
        };
      }
    } catch (error) {
      global.logger?.debug('Could not get pool info: %o', error.message);
    }
    return null;
  }

  // Start monitoring
  startMonitoring() {
    // Log stats every 5 minutes
    setInterval(() => {
      this.logQueryStats();
    }, 5 * 60 * 1000);

    // Health check every 2 minutes
    setInterval(async () => {
      await this.checkHealth();
    }, 2 * 60 * 1000);

    // Pool info every minute in development
    if (process.env.NODE_ENV === 'development') {
      setInterval(() => {
        const poolInfo = this.getPoolInfo();
        if (poolInfo) {
          global.logger?.debug('Pool Info: %o', poolInfo);
        }
      }, 60 * 1000);
    }

    global.logger?.info('Database monitoring started');
  }

  // Increment counters
  incrementQueryCount() {
    this.queryCount++;
  }

  incrementErrorCount() {
    this.errorCount++;
  }
}

// Create singleton instance
const dbMonitor = new DatabaseMonitor();

// Hook into Knex events
db.on('query', (query) => {
  dbMonitor.incrementQueryCount();
  
  const start = Date.now();
  query.response = query.response || {};
  
  // Track query completion time
  const originalThen = query.response.then;
  if (originalThen) {
    query.response.then = function(...args) {
      const duration = Date.now() - start;
      
      // Log slow queries (> 2 seconds)
      if (duration > 2000) {
        dbMonitor.trackSlowQuery(query.sql, duration, query.bindings);
        global.logger?.warn('Slow query detected: %dms - %s', duration, query.sql.substring(0, 100));
      }
      
      return originalThen.apply(this, args);
    };
  }
});

db.on('query-error', (error, query) => {
  dbMonitor.incrementErrorCount();
  global.logger?.error('Query error: %o', {
    error: error.message,
    sql: query.sql.substring(0, 200),
    bindings: query.bindings
  });
});

module.exports = dbMonitor;
