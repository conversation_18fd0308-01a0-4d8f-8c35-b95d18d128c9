import { selectorFamily } from 'recoil';
import { getHweCode } from '@homeworksenergy/utility-service';

// eslint-disable-next-line import/no-cycle
import { calendarTypeAtom } from '@recoil/app/index';
import { capitalizeFirstLetterOfString } from '@utils/functions';
import eventTypesSelectorFamily from './eventTypesSelectorFamily';
import departmentsSelector from './departmentsSelector';

const eventTypeOptionsSelectorFamily = selectorFamily({
  key: 'eventTypeOptions',

  // Currently accepting department name or ID. I'm not sure what the best practice would be here.
  // I don't like using the ID here since it can currently change between dev and prod, and using the integer
  // feels like a code smell (hard to tell what department: 6 means rather than just department: Insulation)
  get: ({ departmentName, departmentId, showGroups = true, state = 'MA', stateCode }) => ({
    get,
  }) => {
    const departments = get(departmentsSelector);

    const department = departments.find(({ name, value }) => {
      if (departmentId) return value === departmentId;
      if (departmentName) return name === departmentName;
      return false;
    });
    let eventStateCode = stateCode;
    if (!stateCode) ({ stateCode: eventStateCode } = getHweCode({ state }));
    let eventType = `${eventStateCode}${department?.eventType}`;
    if (!eventType) eventType = get(calendarTypeAtom);
    if (!eventType) return [];

    const { groups, eventTypes } = get(eventTypesSelectorFamily(eventType));

    return showGroups
      ? Object.keys(groups).map((group) => {
          return {
            key: capitalizeFirstLetterOfString(group),
            value: group,
            eventTypes: groups[group].map(({ eventType, name }) => {
              return { key: name, value: eventType };
            }),
          };
        })
      : eventTypes.map(({ eventType, name }) => {
          return {
            key: name,
            value: eventType,
          };
        });
  },
});

export default eventTypeOptionsSelectorFamily;
