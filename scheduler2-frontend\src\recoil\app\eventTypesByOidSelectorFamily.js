import { selectorFamily } from 'recoil';
import { UtilityManager } from '@utils/APIManager';
import { isDeptPartner } from '@utils/AuthUtils';

const eventTypesByOidSelectorFamily = selectorFamily({
  key: 'eventTypesByOid',
  get: (oid) => async () => {
    if (!oid) return [];

    const isPartner = isDeptPartner();

    // Partners can only create custom blocks.
    // No need to go to the backend if the operator is a partner.
    if (isPartner) {
      return [{ key: 'Custom Block', value: '999999' }];
    }

    const eventTypes = await UtilityManager.getEventTypesByOid(oid);
    return eventTypes.map((option) => {
      // TODO: There might be a better way to do this. Need key/value for the event sidebar dropdown.
      return { key: option.name, value: option.eventType };
    });
  },
});

export default eventTypesByOidSelectorFamily;
