import { selector } from 'recoil';
import { UtilityManager } from '@utils/APIManager';

const regionsSelector = selector({
  key: 'regions',
  get: async () => {
    let regions = await UtilityManager.getAllRegions();
    regions = regions.map(({ name, regionId, abbreviation, state }) => {
      return { key: name, abbreviation, value: regionId, id: regionId, state };
    });
    return regions;
  },
});

export default regionsSelector;
