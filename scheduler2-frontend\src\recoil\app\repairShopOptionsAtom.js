import { atom, selector } from 'recoil';

import { UtilityManager } from '@utils/APIManager';

const repairShopOptionsAtom = atom({
  key: 'repairShopsAtom',
  default: selector({
    key: 'repairShopsSelector',
    get: async () => {
      const repairShops = await UtilityManager.getRepairShopAddresses();
      const formatRepairShops = repairShops.map(({ shop, address }) => {
        return { key: shop, value: address };
      });
      return formatRepairShops;
    },
  }),
});

export default repairShopOptionsAtom;
