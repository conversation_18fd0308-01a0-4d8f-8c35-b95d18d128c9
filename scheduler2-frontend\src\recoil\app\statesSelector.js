import { selector } from 'recoil';
import { UtilityManager } from '@utils/APIManager';

const allStates = [];

const statesSelector = selector({
  key: 'statesSelector',
  get: async () => {
    let statesArray = await UtilityManager.getAllStates();
    statesArray = statesArray.map(
      ({ state: name, name: fullName, abbreviation: value, eventType }) => {
        return {
          key: name,
          name,
          fullName,
          value,
          eventType,
        };
      },
    );
    return statesArray;
  },
});

const setStates = (recoilStates) => {
  allStates.push(...recoilStates);
};

export { statesSelector, allStates, setStates };
