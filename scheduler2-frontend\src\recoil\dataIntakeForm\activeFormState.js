import { atom } from 'recoil';

/**
 * The 'activeForm' object tracks the active status of forms in the DataIntakeForm.
 * By default, it is initialized with null values.
 *
 * When multiple forms are present, 'activeForm' is updated with an object
 * containing the form's index and its active status (e.g., { index: 1, status: true }).
 *
 * This is primarily used in the formSettings state to manage the dynamic rendering
 * of forms based on their index within the DataIntakeForm component.
 *
 * In the future, it might be utilized in other parts of the application as needed.
 */

const activeFormState = atom({
  key: 'activeFormState',
  default: null,
});

export default activeFormState;
