import { createGlobalStyle } from 'styled-components';

const ReactDatePickerStyles = createGlobalStyle`
  .react-datepicker__day--closed:not(.react-datepicker__day--disabled) {
    color: #17174d;
  }

  .react-datepicker__day--open:not(.react-datepicker__day--disabled) {
    color: ${({ theme }) => theme.colors.green};
  }

  .react-datepicker {
    font-size: .75em;
  }

  .react-datepicker__header {
    padding-top: 0.8em;
  }

  .react-datepicker__month {
    margin: 0.4em 1em;
  }

  .react-datepicker__day {
    font-size: 1.5em;
    width: 3em;
    line-height: 3.1em;
    margin: 0.166em;
  }

  .react-datepicker__day-name {
    font-size: 2em;
    line-height: 1.5em;
    width: 2.3em;
    color: #4f5578;
  }

  .react-datepicker__navigation {
    top: 1.5em;
    height: 2em;
  }

  .react-datepicker__navigation--previous {
    border-right-color: #53597b;
  }

  .react-datepicker__navigation--next {
    border-left-color: #53597b;
  }

  .react-datepicker__month-container {
    width: 310px;
    height: 300px;
    font-size: .80em;
  }

  .react-datepicker__month-container .react-datepicker__header {
    background-color: white;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
  }

  .react-datepicker__current-month {
    font-size: 2em;
    color: ${({ theme }) => theme.colors.green};
    font-weight: 500;
  }
`;

export default ReactDatePickerStyles;
