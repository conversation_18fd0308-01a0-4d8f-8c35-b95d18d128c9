// COLORS
const colors = {
  white: '#ffffff',
  black: '#000000',
  veryLightBlue: '#f6f8ff',
  lightBlue: '#acd4f3',
  brightBlue: '#4883c4',
  darkBlue: '#42446e',
  veryDarkBlue: '#17174d',
  veryLightGray: '#f4f4f6',
  lightGray: '#dfe1e6',
  mediumGray: '#9a9aac',
  darkGray: '#7d7d86',
  veryDarkGray: '#3e3e3e',
  closedGray: '#d1d1d1',
  closedBackgroundGray: '#ebebeb',
  transparent: 'rgba(255, 255, 255, 0)',
  lightYellow: '#fef6dd',
  purple: '#800080',
  darkYellow: '#FEE12B',
  greyShadow: 'rgba(23, 23, 77, 0.12)',
  lightGreen: '#dcedc1',
  green: '#26c281',
  green200: '#00802b',
  red: '#e84f46',
  darkGreen: '#7b903a',
  customBlock: '#7900d6',
  shadowBlock: '#d600d6',
  regionHeader: '#231f20',
  calendarBorder: '#949495',

  // Extra names based on your usage
  eventGreen: '#00802b',
  dayFilled: '#dcedc1',
  capJob: '#7b903a',

  // Custom for events
  nameIniBg: '#C29800',
  eventA: '#0076D6',
  eventB: '#AD6500',
  eventC: '#21A671',
  eventD: '#A8A8A8',
  eventE: '#D5DD2D',
  eventF: '#28477B',
  eventG: '#AF1B00',
  eventH: '#9C6902',
  eventI: '#00802B',
  eventJ: '#91225A',
  eventK: '#CF8309',
  eventL: '#12c4c4',
  eventM: '#8A5000',
  eventN: '#FFBF00',
  actionButton: '#00802B',

  // Form / Text
  formText: '#201D1D',
};

// PRIMARY / SECONDARY COLORS
const primary = {
  100: colors.veryLightBlue,
  200: colors.lightBlue,
  300: colors.brightBlue,
  400: colors.darkBlue,
  500: colors.veryDarkBlue,
};

const secondary = {
  100: colors.white,
  200: colors.veryLightGray,
  300: colors.lightGray,
  400: colors.mediumGray,
  500: colors.darkGray,
  600: colors.veryDarkGray,
  700: colors.black,
  800: colors.closedGray,
  900: colors.closedBackgroundGray,
};

const fontSizes = {
  h1: '28px',
  h2: '22px',
  h3: '16px',
  h4: '14px',
  h5: '13px',
};

const breakpoints = {
  mobileS: 320,
  mobileM: 375,
  mobileL: 425,
  tablet: 768,
  laptop: 1024,
  laptopM: 1280,
  laptopL: 1440,
  desktop: 2560,
};

const up = (breakpoint, vertical = false) =>
  `@media (min-${vertical ? 'height' : 'width'}: calc(${breakpoint}px + 0.02px))`;

const down = (breakpoint, vertical = false) =>
  `@media (max-${vertical ? 'height' : 'width'}: ${breakpoint}px)`;

const between = (breakpointMin, breakpointMax, vertical = false) =>
  `@media (max-${vertical ? 'height' : 'width'}: ${breakpointMax}px) and (min-${
    vertical ? 'height' : 'width'
  }: calc(${breakpointMin}px + 0.02px))`;

const screenSize = { up, down, between };
const textAndColor = {
  main: { textTransform: 'uppercase', color: secondary[500] },
  form: { textTransform: 'none', color: colors.formText },
};

const mainTheme = {
  colors,
  primary,
  secondary,
  fontSizes,
  breakpoints,
  screenSize,
  textAndColor,
};
const formTheme = {
  colors,
  primary,
  secondary,
  fontSizes,
  breakpoints,
  screenSize,
  textAndColor,
};

// EXPORT EVERYTHING
export { mainTheme, formTheme };
