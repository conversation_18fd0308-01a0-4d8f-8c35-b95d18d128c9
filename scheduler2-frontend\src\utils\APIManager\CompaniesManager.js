import { handleApiCall } from '@utils/APIManager/utils/AxiosConfig';

const getCompanies = async () => {
  const url = '/api/companies/get';
  const response = await handleApiCall({ url, method: 'get' });
  if (!response) return false;
  const { companies } = response;
  return companies;
};

const createCompany = async (name) => {
  const url = '/api/companies/createCompany';
  const params = {
    name,
  };
  const response = await handleApiCall({
    url,
    method: 'post',
    params,
    loadingMessage: 'Creating new company...',
  });
  if (!response) return false;
  const { company } = response;
  return company;
};

export default { getCompanies, createCompany };
