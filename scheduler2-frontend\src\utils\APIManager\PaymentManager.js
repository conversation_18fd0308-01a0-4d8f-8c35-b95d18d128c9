import { throwError, startLoading, stopLoading } from '@utils/EventEmitter';
import axios from './utils/AxiosConfig';

const getPaymentLink = async (params) => {
  const url = '/api/payment/getPaymentLink';
  startLoading('Generating Payment Link...');
  try {
    const response = await axios.post(url, params);
    stopLoading();
    const { error } = response.data;
    if (error) {
      throwError({
        message: `Failed to get payment link. Error: ${error.message}`,
        params: `Params passed: ${params}`,
      });
      return false;
    }
    return response.data.paymentLink;
  } catch (error) {
    stopLoading();
    throwError(error);
  }
  return false;
};

const emailPaymentLink = async (params) => {
  const url = '/api/payment/emailPaymentLink';
  startLoading('Sending Payment Link...');
  try {
    const response = await axios.post(url, params);
    stopLoading();
    const { error } = response.data;
    if (error) {
      throwError({
        message: 'Failed to send payment link. Error.',
        params: `Params passed: ${params}`,
      });
      return false;
    }
    return true;
  } catch (error) {
    stopLoading();
    throwError(error);
  }
  return false;
};

export default { getPaymentLink, emailPaymentLink };
