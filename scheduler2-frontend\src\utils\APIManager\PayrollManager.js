import { startLoading, stopLoading, throwError, displaySuccessMessage } from '@utils/EventEmitter';
import axios from './utils/AxiosConfig';

// Invoke WX Payroll CSV files lambda
const generateCsvFilesForWxPayroll = async () => {
  try {
    const url = 'https://hosttestapi.homeworksenergy.com/createWxPayrollCsvFiles';
    startLoading('Creating WX Payroll CSV Files...');
    const data = await axios.get(url);
    stopLoading();
    console.log('WX Payroll CSV Files Created ==>', data);
    return data;
  } catch (error) {
    console.log('Error ->', error);
    return throwError(`Request Failed! Please see error ----> ${error}`);
  }
};

// Get date dropdown values from S3
const getUploadedDatesFromS3 = async () => {
  const url = '/api/payroll/getFolderNamesFromS3';
  try {
    startLoading('Loading...');
    const { data } = await axios.get(url);
    stopLoading();
    return data.uploadedDates;
  } catch (error) {
    stopLoading();
    console.log('ERROR Getting Folder Names From S3 ===>', error);
    return throwError(
      `Request to get date select options failed. Please create a FreskDesk ticket with this information. ---> ${error}`,
    );
  }
};

// Get uploaded documents from S3
const getUploadedDocumentsFromS3 = async (bucketPrefix) => {
  const url = '/api/payroll/getUploadedDocuments';
  try {
    startLoading('Loading Uploaded Documents...');
    const {
      data: { uploadedDocs, combinedDoc },
    } = await axios.get(url, { params: { prefix: bucketPrefix } });
    stopLoading();
    return { uploadedDocs, combinedDoc };
  } catch (error) {
    stopLoading();
    console.log('Error getting uploaded documents from S3. --->', error);
    return throwError(
      `Error with request to get uploaded documents from S3. Please create a FreskDesk ticket with this information. ---> ${error}`,
    );
  }
};

// Run QC check lambda
const runQcCheckOnUploadedFiles = async (folderName) => {
  const bucket =
    window.location.host === 'sch.homeworksenergy.com' ? 'finance-pr' : 'finance-pr-test';
  const bucketName = `${bucket}/${folderName}`;
  const url = `https://d9t5mtweo3.execute-api.us-east-1.amazonaws.com/Prod/payroll-qc?bucketName=${bucketName}`;
  try {
    startLoading('Running QC Check...');
    const { data, status } = await axios.get(url);
    stopLoading();
    if (status === 400) {
      return throwError({
        message: `There was an error with the QC check. Please view the message. ${data}`,
        params: data,
      });
    }
    return data;
  } catch (error) {
    stopLoading();
    console.log('ERROR WITH QC CHECK ===>', error);
    return throwError('Error with QC check lambda...');
  }
};

// Run combine file lambda
const runCombineFilesLambda = async (folderName, uploadedDocs) => {
  const isValid = validateUploadedDocsForCombine(uploadedDocs);
  if (!isValid) return false;
  const bucket =
    window.location.host === 'sch.homeworksenergy.com' ? 'finance-pr' : 'finance-pr-test';
  const bucketName = `${bucket}/${folderName}`;
  const url = `https://d9t5mtweo3.execute-api.us-east-1.amazonaws.com/Prod/merge-files?bucketName=${bucketName}`;
  try {
    startLoading('Combining Files... Please Wait...');
    const { status, data } = await axios.post(url);
    stopLoading();
    if (status === 400) {
      throwError({
        message: `There was an error with the combining of files. ${data}`,
        params: data,
      });
      return false;
    }
    displaySuccessMessage('Successfully combined files!');
    return true;
  } catch (error) {
    stopLoading();
    console.log('ERROR WITH COMBINE FILES LAMBDA --->', error);
    return throwError('Error with combine files lambda...');
  }
};

const validateUploadedDocsForCombine = (docArrToCheck) => {
  let valid = true;
  if (docArrToCheck === 0) {
    throwError('No Files Uploaded...');
    valid = false;
  }
  docArrToCheck.forEach((doc) => {
    const { status } = doc;
    if (!status) {
      throwError('Please run quality control before trying to combine.');
      valid = false;
    }
    if (status && status === 'Error') {
      throwError('There are still files with Error. Combine failed...');
      valid = false;
    }
  });
  return valid;
};

// We don't delete files. We rename them deleted_fileName
const deleteUploadedDocumentFromS3 = async (docName, viewingWeek) => {
  const params = {
    doc: docName,
    bucketName: viewingWeek,
  };
  const url = '/api/payroll/deleteFile';
  try {
    startLoading('Deleting File...');
    const { data } = await axios.get(url, { params });
    stopLoading();
    return data.success;
  } catch (error) {
    console.log('Error deleting file from S3 --->', error);
    return throwError('Error deleting file from S3...');
  }
};

const getSignedUrl = async (bucketName, fileKey) => {
  const params = { bucketName, fileKey };
  const url = '/api/payroll/getCombinedFileSignedUrl';
  try {
    startLoading('Getting Url...');
    const {
      data: { signedUrl },
    } = await axios.get(url, { params });
    stopLoading();
    return signedUrl;
  } catch (error) {
    console.log('Failed getting Signed Url For Combined File From S3 Bucket ===>', error);
    return throwError('Error Getting Combined Filed Signed Url...');
  }
};

// Upload file to S3 - Rename file
const uploadDocumentToS3 = async (file, nameOfFile, bucketName) => {
  const url = `/api/payroll/uploadfile/${nameOfFile}/${bucketName}`;
  try {
    startLoading('Uploading File...');
    const { data } = await axios.post(url, file);
    stopLoading();
    if (!data.success) {
      return throwError(data.message);
    }
    return data.success;
  } catch (error) {
    stopLoading();
    console.log('Error Uploading File To S3 -->', error);
    return throwError('Error Uploading File To S3...');
  }
};

const uploadMoneyCollectionDocument = async (file) => {
  const url = '/api/payroll/handleMoneyCollectionDocument';
  try {
    startLoading('Processing file...');
    const { data } = await axios.post(url, file);
    stopLoading();
    if (!data.success) {
      return throwError({
        message: `${data.message}}`,
      });
    }
    const { errors, csvFileErrors, fileName } = data.data;
    if (!errors.length) {
      displaySuccessMessage('Successfully Uploaded File To Salesforce.');
      return false;
    }
    return { errors, csvFileErrors, fileName };
  } catch (error) {
    stopLoading();
    console.log('Error uploading and parsing money collection file:', error);
    return throwError('Error uploading and parsing money collection file...');
  }
};

const uploadMulitpleDocumentsToS3 = async (filesToUpload, bucketName) => {
  try {
    if (filesToUpload.length === 0) {
      return throwError('No Files Selected To Upload. Please Try Again...');
    }
    const filesFailedToUpload = [];
    startLoading('Uploading Files...');
    for (let k = 0; k < filesToUpload.length; k++) {
      const file = filesToUpload[k];
      const { name } = file;
      const fileName = name.split('.').shift();
      const url = `/api/payroll/uploadfile/${fileName}/${bucketName}`;
      const fileData = new FormData();
      fileData.append('file', file);
      // eslint-disable-next-line no-await-in-loop
      const { data } = await axios.post(url, fileData);
      if (!data.success) {
        filesFailedToUpload.push(fileName);
      }
    }
    stopLoading();
    if (filesFailedToUpload.length > 0) {
      const filesStr = filesFailedToUpload.join(', ');
      throwError(`Some or All Files Failed To Upload ---> ${filesStr}`);
    }
    return true;
  } catch (error) {
    stopLoading();
    console.log('Error Uploading Multiple Files ===>', error);
    return throwError('Error Uploading Multiple Files To S3...');
  }
};

const uploadMoneyCollectionErrorFile = async (file) => {
  const url = '/api/payroll/handleMoneyCollectionErrorFile';
  try {
    startLoading('Processing file...');
    const { data } = await axios.post(url, file);
    stopLoading();
    if (!data.success) {
      return throwError({
        message: `${data.message}}`,
      });
    }
    const { processedErrorFileData } = data;
    displaySuccessMessage('Successfully Processed Error File.');
    return processedErrorFileData;
  } catch (error) {
    stopLoading();
    console.log('Error processing error file:', error);
    return throwError('Error processing error file...', error);
  }
};

export default {
  generateCsvFilesForWxPayroll,
  getUploadedDatesFromS3,
  getUploadedDocumentsFromS3,
  runQcCheckOnUploadedFiles,
  deleteUploadedDocumentFromS3,
  uploadDocumentToS3,
  uploadMoneyCollectionDocument,
  uploadMulitpleDocumentsToS3,
  getSignedUrl,
  runCombineFilesLambda,
  uploadMoneyCollectionErrorFile,
};
