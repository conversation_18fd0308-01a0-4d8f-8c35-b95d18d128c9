import { startLoading, stopLoading, throwError, displaySuccessMessage } from '@utils/EventEmitter';
import axios, { handleApiCall } from './utils/AxiosConfig';

const getObject = async (objectType, salesforceId, sfType = '1.0') => {
  const url = `/api/salesforce/getObject?objectName=${objectType}&id=${salesforceId}&sfType=${sfType}`;
  startLoading('Getting info from Salesforce...');
  const response = await axios.get(url);
  stopLoading();
  const { sfObject, error } = response.data;
  if (error) {
    // Handle where this is used
    console.log('Error: ', error);
    return null;
  }
  return sfObject;
};

const getHvacContractObjectsWithDealId = async (dealId, accountId = '') => {
  const url = `/api/salesforce/getHvacContractObjectsWithDealId?dealId=${dealId}&accountId=${accountId}`;
  startLoading('Getting info from Salesforce...');
  const response = await axios.get(url);
  stopLoading();
  const { contractObjects, error } = response.data;
  if (error) throwError(error);
  return contractObjects;
};

const getSiteEvalDetails = async (id) => {
  const url = `/api/salesforce/getSiteEvalDetails?opportunityId=${id}`;
  try {
    startLoading('Getting info from Salesforce...');
    const response = await axios.get(url);
    stopLoading();
    const { opportunityDetails, error } = response.data;
    if (error) throwError(error);
    return opportunityDetails;
  } catch (error) {
    stopLoading();
    return throwError({
      message: 'Problem getting Oppurtunity details.',
      params: `${id}`,
    });
  }
};

const getUserWithId = async (sfId) => {
  const url = `/api/salesforce/getUserWithId?sfId=${sfId}`;
  const response = await axios.get(url);
  const { isValid, name, error } = response.data;
  if (error) throwError(error);
  return { isValid, name };
};

// Update Salesforce object depending on state and department with values from ResultingQuestions
const updateSfObject = async (sfObjToUpdate, sfObjParams) => {
  const axiosParamsObj = {
    sfObjToUpdate,
    sfObjParams,
  };
  try {
    startLoading('Updating Salesforce...');
    const { data } = await axios.post('/api/salesforce/updateResultingQuestions', axiosParamsObj);
    stopLoading();
    if (data.error) throw data.error;
    return data;
  } catch (error) {
    const message =
      sfObjToUpdate === 'Opportunity'
        ? error?.message
        : 'Problem updating SalesForce with answers.';
    return throwError({
      message,
      params: `${sfObjParams}`,
    });
  }
};

// Gets all fields and values from Salesforce object.
// Compares the resultingQuestion.questions's sfField with value on Salesforce.
// If the value from Salesforce is not null then this visit has been resulted before.
// If resulted before, show DocumentTable but if it has not been resulted then show ResultingQuestions
// eslint-disable-next-line consistent-return
const getResultStatus = async (sfObjectForResulting, sfFieldsToCheck, sfUniqueId) => {
  const paramsObj = {
    params: {
      sfObjectForResulting,
      sfUniqueId,
    },
  };
  let resultStatus = 'firstTimeHere';
  let locked = true;
  try {
    const response = await axios.get('/api/salesforce/getResultStatus', paramsObj);
    const { currentValue, sfTargetName } = sfFieldsToCheck[0];
    if (response.data[sfTargetName] === null) {
      return throwError({
        message:
          'Salesforce Field Issue. WT Visit Result MUST have a value on Salesforce! Walkthrough Visit Result on HVAC Contract cannnot be null. Please select and save a value on Salesforce.',
      });
    }
    if (currentValue !== response.data[sfTargetName]) {
      resultStatus = 'beenHereBefore';
      locked = false;
    }
    sfFieldsToCheck.forEach((questionObj) => {
      const { sfTargetName, notes } = questionObj;
      // eslint-disable-next-line no-param-reassign
      questionObj.currentValue = response.data[sfTargetName];
      if (notes && response.data[notes.sfField]) notes.notes = response.data[notes.sfField];
    });
    return {
      updatedQuestions: sfFieldsToCheck,
      status: resultStatus,
      locked,
    };
  } catch (error) {
    throwError({
      message: `Problem retrieving result status. Error: ${error}`,
      params: `Id: ${sfUniqueId}`,
    });
  }
};

const getInsulationEventInfoWithOperationsId = async (operationsId) => {
  try {
    const { data: sfResponse } = await axios.get(
      `/api/salesforce/getInsulationEventInfoWithOperationsId/${operationsId}`,
    );
    const { error } = sfResponse;
    if (error) return throwError(error.message);

    return sfResponse;
  } catch (error) {
    return throwError({
      message: `Failed to get Salesforce information for operations id ${operationsId}`,
    });
  }
};

const getHEAEventInfoWithDealIds = async (dealIds) => {
  const response = await handleApiCall({
    method: 'get',
    url: `/api/salesforce/getHEAEventInfoWithDealIds/${dealIds.join(',')}`,
    loadingMessage: 'Getting info from Salesforce...',
  });

  if (!response) return false;

  return response;
};

// TODO: can remove the other getCTHEAEventInfo functions
const getCTHEAEventInfo = async ({ objectName, sfId }) => {
  const response = await handleApiCall({
    method: 'get',
    url: `/api/salesforce/getCTHEAEventInfo/${objectName}/${sfId}`,
    loadingMessage: 'Getting info from Salesforce...',
  });

  if (!response) return false;

  return response;
};

const getCTHEAEventInfoWithAccountId = async (accountId, workVisitId) => {
  const response = await handleApiCall({
    method: 'get',
    url: `/api/salesforce/getCTHEAEventInfoWithAccountId/${accountId}/${workVisitId}`,
    loadingMessage: 'Getting info from Salesforce...',
  });

  if (!response) return false;

  // TODO: do we need to support sfId updates after create?
  // This erases the multi unit sfIds if we don't delete this field, so removing for now
  // Otherwise we need to update to get all of the sfIds
  delete response.sfIds;

  return response;
};

const getHVACSalesInfoWithAccountId = async (accountIds) => {
  const response = await handleApiCall({
    method: 'get',
    url: `/api/salesforce/getHVACSalesInfoWithAccountId/${accountIds}`,
    loadingMessage: 'Getting info from Salesforce...',
  });

  if (!response) return false;

  return response;
};

const syncAllCalendarEvents = async (startDate, endDate) => {
  try {
    startLoading('Getting Info from Salesforce...');
    const params = { startDate, endDate };
    const { data: sfResponse } = await axios.get('/api/salesforce/syncAllCalendarEvents', {
      params,
    });
    stopLoading();
    const { error, message } = sfResponse;
    if (error) return throwError(error.message);
    displaySuccessMessage(message);
    return sfResponse;
  } catch (error) {
    stopLoading();
    return throwError({
      message: 'Failed to get Salesforce information',
    });
  }
};

const createMainAccountAndSubAccount = async (data) => {
  try {
    startLoading('Creating Accounts on Salesforce...');
    const resonse = await axios.post('/api/salesforce/createMainAccountandSubAccount', {
      body: data,
    });
    stopLoading();
    return resonse.data;
  } catch (error) {
    stopLoading();
    return throwError({
      message: 'Failed to get Salesforce information',
    });
  }
};

const getDealIdFromAccount = async (id) => {
  try {
    startLoading('Getting Info from Salesforce...');
    const resonse = await axios.get(`/api/salesforce/getDealIdFromAccount?accountId=${id}`);
    stopLoading();
    return resonse.data;
  } catch (error) {
    stopLoading();
    return throwError({
      message: 'Failed to get Salesforce information',
    });
  }
};
const getPartnersEventInfoWithAccountIds = async (accountIds) => {
  const response = await handleApiCall({
    method: 'get',
    url: `/api/salesforce/getPartnersEventInfoWithAccountIds/${accountIds.join(',')}`,
    loadingMessage: 'Getting info from Salesforce...',
  });

  if (!response) return false;

  return response;
};

const getExistingDuplicateRecords = async (formValues, objectType, sfType = '1.0') => {
  try {
    const searchTerms = {
      cell_phone: formValues.customerPrimaryPhoneNumber,
      address: formValues.customerAddress.displayAddress,
      email: formValues.customerEmail,
    };

    const data = {
      objectType,
      searchTerms,
      sfType,
    };

    const url = '/api/salesforce/getExistingDuplicateLeadsAndAccounts/';
    startLoading(`Checking Duplicate ${objectType} Records...`);
    const response = await axios.post(url, data);
    stopLoading();
    return response.data;
  } catch (error) {
    stopLoading();
    return throwError({
      message: error,
    });
  }
};

const updateLead = async (data) => {
  try {
    const url = '/api/salesforce/updateLead/';
    startLoading('Updating Leading...');
    const response = await axios.post(url, data);
    stopLoading();
    return response.data;
  } catch (error) {
    stopLoading();
    return throwError({
      message: error,
    });
  }
};

const convertLead = async (leadId) => {
  try {
    const convertObject = {
      leadId,
      convertedStatus: 'Qualified',
      doNotCreateOpportunity: true,
    };
    const url = '/api/salesforce/convertLead/';
    startLoading('Converting Lead...');
    const response = await axios.post(url, convertObject);
    stopLoading();
    return response.data;
  } catch (error) {
    stopLoading();
    return throwError({
      message: error,
    });
  }
};

const getCustomerInfoFromLead = async (leadId, sfType) => {
  try {
    const url = `/api/salesforce/getObject/?objectName=Lead&id=${leadId}&sfType=${sfType}`;
    startLoading('Getting Customer Details...');
    const response = await axios.get(url);
    stopLoading();
    return response.data;
  } catch (error) {
    stopLoading();
    return throwError({
      message: 'Failed to get Customer Details From Lead',
    });
  }
};

const checkSiteIdDuplication = async (siteIds) => {
  try {
    const url = '/api/salesforce/checkSiteIdDuplication';
    startLoading('Checking Site IDs...');
    const response = await axios.post(url, siteIds);
    stopLoading();
    return response.data;
  } catch (error) {
    stopLoading();
    return throwError({
      message: 'Failed to verify Site ID, Please try again...',
    });
  }
};

const processAccount = async (params) => {
  try {
    const url = '/api/salesforce/processAccount';
    startLoading('Processing Account...');
    const response = await axios.post(url, params);
    stopLoading();

    const { error } = response.data;
    // Not sure if the "|| error" is necessary
    // In the current situation the error is returned under error.message.
    // Not sure if there are situations where it's returned directly as error,
    // so keeping it just in case for now
    if (error) throw Error(error.message || error);

    return response.data;
  } catch (error) {
    stopLoading();
    return throwError(error);
  }
};

export default {
  convertLead,
  updateLead,
  getExistingDuplicateRecords,
  getObject,
  getHvacContractObjectsWithDealId,
  getUserWithId,
  updateSfObject,
  getResultStatus,
  getInsulationEventInfoWithOperationsId,
  syncAllCalendarEvents,
  getHEAEventInfoWithDealIds,
  getCTHEAEventInfo,
  getCTHEAEventInfoWithAccountId,
  getHVACSalesInfoWithAccountId,
  getSiteEvalDetails,
  createMainAccountAndSubAccount,
  getDealIdFromAccount,
  getCustomerInfoFromLead,
  getPartnersEventInfoWithAccountIds,
  checkSiteIdDuplication,
  processAccount,
};
