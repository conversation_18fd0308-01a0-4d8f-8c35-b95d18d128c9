import moment from 'moment';
import { displaySuccessMessage, throwError, startLoading, stopLoading } from '@utils/EventEmitter';
import axios, { handleApiCall } from './utils/AxiosConfig';

const getUserInfo = async (userId) => {
  const url = `/api/users/getUserWithOid/${userId}`;
  startLoading('Getting User Info...');
  const response = await axios.get(url);
  const { user, error } = response.data;
  if (error) throwError(error);
  stopLoading();
  return user;
};

const getUsersForEventType = async (eventType) => {
  const url = `/api/users/getUsersForEventType?eventType=${eventType}`;
  startLoading('Getting Users For Event Type...');
  const response = await axios.get(url);
  const { users, error } = response.data;
  if (error) throwError(error);
  stopLoading();
  return { users };
};

const getWalkthroughTech = async (contractId) => {
  const url = `/api/users/getWalkthroughTech?contractId=${contractId}`;
  startLoading('Getting WalkThrough Tech...');
  const response = await axios.get(url);
  const { tech, error } = response.data;
  if (error) throwError(error);
  stopLoading();
  return tech.displayName;
};

const getUsersInfo = async () => {
  const url = '/api/users/getAllUsers';
  startLoading('Getting All Users...');
  const { data } = await axios.get(url);
  const { error } = data;
  if (error) throwError(error);
  stopLoading();
  return data;
};

const updateUser = async (params) => {
  const url = '/api/users/updateUser';
  const response = await handleApiCall({
    url,
    method: 'put',
    params,
    loadingMessage: 'Updating User...',
    successMessage: 'Successfully updated user info.',
  });
  if (!response) return false;
  return response;
};

const getUserRoles = async (oid) => {
  const url = `/api/users/roles/${oid}`;
  startLoading('Getting User Roles...');
  const response = await axios.get(url);
  const { error } = response.data;
  if (error) throwError(error);
  stopLoading();
  return response.data;
};

const updateUserRoles = async (oid, roles) => {
  const url = '/api/users/updateRoles';
  const params = { oid, role: roles };
  const response = await handleApiCall({
    url,
    method: 'put',
    params,
    loadingMessage: 'Updating User Roles...',
    successMessage: 'Successfully updated user roles.',
  });
  if (!response) return false;
  return response;
};

const deleteUserRoles = async (oid, departmentId, state) => {
  const params = { oid, departmentId, state };
  const url = 'api/users/deleteUserRoles';
  startLoading('Deleting User Role...');
  const { data } = await axios.delete(url, { data: params });
  const { error } = data;
  if (error) return throwError(error);
  stopLoading();
  return data;
};

const deactivateUser = async (userOid) => {
  const url = '/api/users/deactivateUser';
  startLoading('Deactivating User...');
  const response = await axios.put(url, { userOid });
  const { error } = response.data;
  if (error) throwError(error);
  stopLoading();
  return response.data;
};

const getCurrentUserEventsForToday = async () => {
  const url = '/api/users/getCurrentUserEventsForToday';
  startLoading('Checking for events...');
  const { data } = await axios.get(url);
  const { error } = data;
  if (error) return throwError(error);
  stopLoading();
  return data;
};

const getOverviewForUser = async (oid, startDate, endDate) => {
  const url = `/api/users/getOverviewForUser?oid=${oid}&startDate=${startDate}&endDate=${endDate}`;
  startLoading('Getting Overview for User');
  const { data } = await axios.get(url);
  const { error } = data;
  stopLoading();
  if (error) return throwError(error);
  return data;
};

const updateUserOverview = async (params) => {
  const { dates } = params;

  const url = '/api/users/updateUserOverview';
  startLoading('Updating availability');
  const { data } = await axios.post(url, params);

  const { error } = data;
  stopLoading();
  if (error) return throwError(error);
  // We opened the calendar for 2 months out but we want to call to trigger again next month.
  // Subtract a month so this will trigger again next month
  const dateToStore = moment(dates[0])
    .subtract(1, 'month')
    .format('MM/DD/YYYY');
  await setUserLastUpdatedSwapMonth({ date: dateToStore });

  displaySuccessMessage('Successfully updated availability');

  return data;
};

const getUserLastUpdatedSwapMonth = async () => {
  const url = '/api/users/getUserLastUpdatedSwapMonth';
  const { data } = await axios.get(url);
  const { error } = data;
  if (error) return throwError(error);
  return data;
};

const setUserLastUpdatedSwapMonth = async ({ date }) => {
  const url = `/api/users/setUserLastUpdatedSwapMonth?date=${date}`;
  const { data } = await axios.put(url);
  const { error } = data;
  if (error) return throwError(error);
  return data;
};

const getUsersOfCrewType = async (departmentId) => {
  const url = `/api/users/getUsersOfCrewType/${departmentId}`;
  const { data } = await axios.get(url);
  const { error } = data;
  if (error) return throwError(error);
  return data;
};

export default {
  getUserInfo,
  getUsersForEventType,
  getUsersInfo,
  updateUser,
  getUserRoles,
  updateUserRoles,
  deleteUserRoles,
  getWalkthroughTech,
  deactivateUser,
  getCurrentUserEventsForToday,
  getOverviewForUser,
  updateUserOverview,
  getUserLastUpdatedSwapMonth,
  setUserLastUpdatedSwapMonth,
  getUsersOfCrewType,
};
