import { handleApiCall } from '@utils/APIManager/utils/AxiosConfig';

const uploadWorkReceiptTemplate = async ({ file, state, type }) => {
  const url = `/api/workReceipt/uploadWorkReceiptTemplate/${state}/${type}`;
  const response = await handleApiCall({
    url,
    method: 'post',
    params: file,
    loadingMessage: 'Uploading File ...',
    successMessage: 'Successfully uploaded new template...',
  });
  if (!response) return false;
  return true;
};

const getResultingInfo = async (params) => {
  const { dealId, accountId } = params;

  const url = '/api/workReceipt/getResultingInfo';

  const response = await handleApiCall({
    method: 'post',
    url,
    params: { dealId, accountId },
    loadingMessage: 'Getting resulting information for this customer...',
  });
  if (!response) return false;
  return response;
};

const getExistingWorkReceipt = async (eventId) => {
  const url = '/api/workReceipt/getExistingWorkReceipt';

  const response = await handleApiCall({
    url,
    method: 'get',
    params: { eventId },
    loadingMessage: 'Checking for existing work receipt...',
  });

  if (!response) return false;
  return response;
};

const saveWorkReceipt = async (workReceiptValues) => {
  const url = '/api/workReceipt/saveWorkReceipt/';
  const response = await handleApiCall({
    url,
    method: 'post',
    params: workReceiptValues,
    loadingMessage: 'Saving work receipt...',
    successMessage: 'Successfully saved work receipt',
  });
  if (!response) return false;
  return response;
};

const getDealDetailsForWorkReceipt = async (dealId) => {
  const url = `/api/populateDealsDetailsFromUplight/${dealId}`;
  const response = await handleApiCall({
    url,
    method: 'get',
    loadingMessage: ' Fetching Deal(s) Details...',
  });
  if (!response) return false;
  return response;
};

const generateWorkReceipt = async (data, workReceiptMode) => {
  const url = `/api/workReceipt/generateWorkReceipt?workReceiptMode=${workReceiptMode}&state=MA`;
  const response = await handleApiCall({
    url,
    method: 'post',
    params: data,
    loadingMessage: 'Generating Work Receipt(s)...',
    successMessage: 'Successfully Work Receipt(s) Generated...',
  });
  if (!response) return false;
  return response;
};

const generateOfflineWorkReceipt = async (data) => {
  const url = '/api/workReceipt/generateOfflineWorkReceipt';
  const response = await handleApiCall({
    url,
    method: 'post',
    params: data,
    loadingMessage: 'Generating Work Receipt(s)...',
    successMessage: 'Successfully Work Receipt(s) Generated...',
  });
  if (!response) return null;
  return response;
};

export default {
  uploadWorkReceiptTemplate,
  getResultingInfo,
  getExistingWorkReceipt,
  saveWorkReceipt,
  getDealDetailsForWorkReceipt,
  generateWorkReceipt,
  generateOfflineWorkReceipt,
};
