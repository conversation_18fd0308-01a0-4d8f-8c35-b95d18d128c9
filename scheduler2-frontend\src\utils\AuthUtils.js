import { isHWEUser } from '@homeworksenergy/utility-service';
import { throwError } from '@utils/EventEmitter';
import { SessionManager } from '@utils/APIManager';
import { allStates } from '@recoil/app';

const getUserCookie = () => {
  let user = null;
  const userString = localStorage.getItem('user');
  if (userString) {
    user = JSON.parse(userString);
  } else {
    return throwError('Could not identify user. Please contact software.');
  }
  if (user.roles.length === 0) user.roles.push({ role: '', department: '' });
  return user;
};

const setUserCookie = async () => {
  const user = await SessionManager.getCurrentUserInfo();
  localStorage.setItem('user', JSON.stringify(user));
};

const isAuthorized = (minRole, department, allowExternal = false, stateCode) => {
  const allStateCodes =
    allStates?.map(({ value }) => {
      return value;
    }) || [];
  const allStateEventTypes =
    allStates?.map(({ eventType }) => {
      return eventType;
    }) || [];

  const stateCodes = stateCode ? [stateCode] : allStateCodes;
  const stateEventTypes = stateCode
    ? [allStates.find((state) => state.value === stateCode)?.eventType]
    : allStateEventTypes;

  const formattedDepartment = department?.replace(' ', '-');
  const { roles: userRolesWithDepartments, company, isManager } = getUserCookie();
  const allRoles = ['Agent', 'External Scheduler', 'Basic', 'Scheduler', 'Manager', 'Super User'];
  const hweUser = isHWEUser({ company });

  // get all the authorized roles upto minRole supplied
  const authorizedRoles = allRoles.slice(allRoles.indexOf(minRole));
  // Don't allow external companies if allowExternal is not set
  if (!allowExternal && !hweUser) return false;

  const allowAllDepartments = ['All', 'N/A', undefined, null].includes(formattedDepartment);

  // Filter down to relevant roles
  // If a department is set, we only care about that department
  // If not or 'all', take all roles
  const roles = userRolesWithDepartments
    .map((role) => {
      if (
        (role.department.includes(formattedDepartment) || allowAllDepartments) &&
        stateCodes.includes(role.stateAbbr)
      ) {
        return role;
      }
      return '';
    })
    .filter((role) => role);

  // If they have no permissions for the department, they are not authorized
  if (!roles.length) return false;

  // For HEA, the managers need to be agents to be shown on the scheduler,
  // but also need to function like regular HEA managers
  const isAgentManager =
    !!roles.find(({ role, state }) => {
      return role === 'Agent' && stateEventTypes.includes(state);
    }) && isManager;

  const agentManagerAuthorized = authorizedRoles.includes('Manager') && isAgentManager;

  // Get just the highest role the user has to check against the authorized roles
  const highestRole = roles.reduce((prev, current) => {
    return prev.roleId < current.roleId ? prev : current;
  });

  // Handle cross department authorization
  // TODO: Added 'N/A' to allow lock and pinning of custom blocks if you're a manager of wx or hvac, etc.
  // might want to use 'calendarTypeAtom' in the future to make sure that you're the appropriate manager for the calendar you're on
  const result = authorizedRoles.includes(highestRole.role) || agentManagerAuthorized;
  return result;
};

// getting those departments which User is authorized to perform func of Managers
const getAuthorizedDepartments = (requestedState = '*') => {
  const authRoles = ['Manager', 'Super User'];
  const { roles, isManager } = getUserCookie();
  const departments = roles.reduce(
    (result, { role, department, departmentId, state, stateAbbr }) => {
      if (![state, '*'].includes(requestedState)) return result;
      // For HEA, the managers need to be agents to be shown on the scheduler,
      // but also need to function like regular HEA managers
      const isAgentManager = role === 'Agent' && isManager;
      if (authRoles.includes(role) || isAgentManager)
        result.push({ department, departmentId, state, stateAbbr });
      return result;
    },
    [],
  );
  return departments;
};

const hasRole = (
  authorizedRole,
  authorizedDepartment,
  authorizedStateAbbr = null,
  userRoles = null,
) => {
  const { roles = [] } = userRoles || getUserCookie();
  return roles
    .map(({ department, role, stateAbbr }) => {
      if (!authorizedDepartment) return role;
      if (department === authorizedDepartment) {
        if (!authorizedStateAbbr || authorizedStateAbbr === stateAbbr) return role;
      }
      return false;
    })
    .includes(authorizedRole);
};

const isDeptPartner = (department) => {
  const { roles, company } = getUserCookie();
  const hweUser = isHWEUser({ company });
  const hasRoleForDepartment =
    !department ||
    !!roles.filter(({ departmentId }) => {
      return departmentId === department;
    }).length;
  const isPartner = !hweUser && hasRoleForDepartment;
  return isPartner;
};

export {
  isAuthorized,
  getUserCookie,
  setUserCookie,
  getAuthorizedDepartments,
  hasRole,
  isDeptPartner,
};
