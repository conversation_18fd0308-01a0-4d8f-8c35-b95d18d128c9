/**
 * Found here: https://github.com/react-dnd/react-dnd/issues/553
 * Allows dragging to the bottom of the calendar to scroll to see more calendar rows
 * TODO: monitor react-dnd for support for this
 */

/*------------------------------------------------*/
/* LIBRARIES
/*------------------------------------------------*/
import throttle from 'lodash/throttle';

/*------------------------------------------------*/
/* CONSTANTS
/*------------------------------------------------*/
const OFFSET = 50; // This is the top/bottom offset you use to start scrolling in the div.
const PX_DIFF = 6;

/*------------------------------------------------*/
/* GLOBAL VARIABLES
/*------------------------------------------------*/
let scrollIncrement = 0;
let isScrolling = false;
let scrollElement = null;
let scrollElementHeight = 0;
let clientRectBottom = 0;
let clientRectTop = 0;

/*------------------------------------------------*/
/* METHODS
/*------------------------------------------------*/

/**
 * Scroll up in the sidebar.
 */
const goUp = () => {
  scrollIncrement -= PX_DIFF;
  scrollElement.scrollTop = scrollIncrement;

  if (isScrolling && scrollIncrement >= 0) {
    window.requestAnimationFrame(goUp);
  }
};

/**
 * Scroll down in the sidebar.
 */
const goDown = () => {
  scrollIncrement += PX_DIFF;
  scrollElement.scrollTop = scrollIncrement;

  if (isScrolling && scrollIncrement <= scrollElementHeight) {
    window.requestAnimationFrame(goDown);
  }
};

const onDragOver = (event) => {
  const isMouseOnTop =
    scrollIncrement >= 0 && event.clientY > clientRectTop && event.clientY < clientRectTop + OFFSET;
  const isMouseOnBottom =
    scrollIncrement <= scrollElementHeight &&
    event.clientY > clientRectBottom - OFFSET &&
    event.clientY <= clientRectBottom;

  if (!isScrolling && (isMouseOnTop || isMouseOnBottom)) {
    isScrolling = true;
    scrollIncrement = scrollElement.scrollTop;

    if (isMouseOnTop) {
      window.requestAnimationFrame(goUp);
    } else {
      window.requestAnimationFrame(goDown);
    }
  } else if (!isMouseOnTop && !isMouseOnBottom) {
    isScrolling = false;
  }
};

/**
 * The "throttle" method prevents executing the same function SO MANY times.
 */
const throttleOnDragOver = throttle(onDragOver, 150);

const addEventListenerForDragScroll = (elementId) => {
  // In Chrome the scrolling works.
  if (navigator.userAgent.indexOf('Chrome') === -1) {
    scrollElement = document.getElementById(elementId);
    scrollElementHeight = scrollElement.scrollHeight;
    const clientRect = scrollElement.getBoundingClientRect();
    clientRectTop = clientRect.top;
    clientRectBottom = clientRect.bottom;

    scrollElement.addEventListener('dragover', throttleOnDragOver);
  }
};

const removeEventListenerForDragScroll = () => {
  isScrolling = false;

  if (scrollElement) {
    scrollElement.removeEventListener('dragover', throttleOnDragOver);
  }
};

/*------------------------------------------------*/
/* EXPORTS
/*------------------------------------------------*/
export default {
  addEventListenerForDragScroll,
  removeEventListenerForDragScroll,
};
