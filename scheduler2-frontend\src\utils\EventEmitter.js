import moment from 'moment';

const LoadingEvent = (message) =>
  new CustomEvent(EVENT_TYPES.loading, {
    detail: { message },
  });

const LoadedEvent = () => new CustomEvent(EVENT_TYPES.loaded);

const SuccessEvent = (success) => {
  return new CustomEvent(EVENT_TYPES.success, { detail: { success } });
};

const ErrorEvent = (error) => {
  return new CustomEvent(EVENT_TYPES.error, { detail: { error } });
};

const ReloadCalendarEvent = (params) => {
  const { startDate, key } = params;
  return new CustomEvent(EVENT_TYPES.reloadCalendar, {
    detail: { startDate, key },
  });
};
const EventChangedEvent = () => new CustomEvent(EVENT_TYPES.eventChanged);

const UpdateAllEventsEvent = (newValue) => {
  return new CustomEvent(EVENT_TYPES.updateAllEvents, { detail: { newValue } });
};

const EVENT_TYPES = {
  loading: 'LOADING',
  loaded: 'LOADED',
  error: 'ERROR',
  success: 'SUCCESS',
  reloadCalendar: 'RELOAD_CALENDAR',
  eventChanged: 'EVENT_CHANGED',
  updateAllEvents: 'UPDATE_ALL_EVENTS',
};

const EVENTS = {
  [EVENT_TYPES.loading]: LoadingEvent,
  [EVENT_TYPES.loaded]: LoadedEvent,
  [EVENT_TYPES.success]: SuccessEvent,
  [EVENT_TYPES.error]: ErrorEvent,
  [EVENT_TYPES.reloadCalendar]: ReloadCalendarEvent,
  [EVENT_TYPES.eventChanged]: EventChangedEvent,
  [EVENT_TYPES.updateAllEvents]: UpdateAllEventsEvent,
};

const dispatchEvent = (eventType, params) => {
  document.body.dispatchEvent(EVENTS[eventType](params));
};

const addEventListener = (eventName, func) => {
  document.body.addEventListener(eventName, func);
  return () => document.body.removeEventListener(eventName, func);
};

const addStartLoadingListener = (func) => {
  return addEventListener(EVENT_TYPES.loading, (event) => func(event.detail.message));
};

const addStopLoadingListener = (func) => {
  return addEventListener(EVENT_TYPES.loaded, func);
};

const addEventChangedListener = (func) => {
  return addEventListener(EVENT_TYPES.eventChanged, func);
};

const addReloadCalendarListener = (func) => {
  return addEventListener(EVENT_TYPES.reloadCalendar, (event) => {
    const { startDate, key } = event.detail;

    func(startDate, key);
  });
};

const addSuccessListener = (func) => {
  return addEventListener(EVENT_TYPES.success, (event) => func(event.detail.success));
};

const addErrorListener = (func) => {
  return addEventListener(EVENT_TYPES.error, (event) => func(event.detail.error));
};

const addUpdateAllEventsListener = (func) => {
  return addEventListener(EVENT_TYPES.updateAllEvents, (event) => {
    const { newValue } = event.detail;
    func(newValue);
  });
};

const startLoading = (message) => {
  dispatchEvent(EVENT_TYPES.loading, message);
};

const stopLoading = () => {
  dispatchEvent(EVENT_TYPES.loaded);
};

const eventChanged = () => {
  dispatchEvent(EVENT_TYPES.eventChanged);
};

const reloadCalendar = (startDate = moment(), key = '') => {
  dispatchEvent(EVENT_TYPES.reloadCalendar, { startDate, key });
};

const displaySuccessMessage = (success) => {
  dispatchEvent(EVENT_TYPES.success, success);
  return true;
};

const throwError = (error) => {
  dispatchEvent(EVENT_TYPES.error, error);
  return false;
};

const updateAllEvents = (newValue) => {
  dispatchEvent(EVENT_TYPES.updateAllEvents, newValue);
};

export {
  startLoading,
  stopLoading,
  eventChanged,
  reloadCalendar,
  displaySuccessMessage,
  throwError,
  updateAllEvents,
  addStartLoadingListener,
  addStopLoadingListener,
  addEventChangedListener,
  addReloadCalendarListener,
  addSuccessListener,
  addErrorListener,
  addUpdateAllEventsListener,
};
