const resultingRequiredDocs = {
  'HEA-CAP': {
    required: [],
    visitResult: {
      'Qualified Out': {
        required: ['WAIVERorLTA', 'CST', 'PICS', 'PV'],
      },
      'Closed Won': {
        required: ['WX', 'WR-excel', 'HEA', 'BAS', 'CST', 'PICS', 'PV', 'PT'],
      },
      'High Prob': {
        required: ['WX', 'WR-excel', 'HEA', 'BAS', 'CST', 'PICS', 'PV', 'PT'],
      },
      'Pre-Weatherization Barrier': {
        required: ['WAIVERorLTA', 'WX', 'WR-excel', 'HEA', 'BAS', 'CST', 'PICS', 'PV', 'PT', 'WD'],
      },
      'Not in EM Home': {
        required: ['WAIVERorLTA', 'WX', 'WR-excel', 'HEA', 'BAS', 'CST', 'PICS', 'PV', 'PT'],
      },
    },
    ampRun: {
      yes: { required: ['ISM'] },
    },
    electricProvider: {
      NG: { required: ['ODR'] },
    },
    atticWork: {
      yes: { required: ['VENT', 'AIF'] },
    },
    knt: {
      yes: { required: ['PWB'] },
    },
    lta: {
      yes: { required: ['WAIVERorLTA'] },
    },
    recommendFridge: {
      yes: { required: ['FRD'] },
    },
    recommendFreezer: {
      yes: { required: ['FRZ'] },
    },
    recommendWasher: {
      yes: { required: ['WAS'] },
    },
    recommendDehumidifier: {
      yes: { required: ['DEH'] },
    },
    recommendWindowAcUnit: {
      yes: { required: ['AC'] },
    },
    displaceGasHeatWithHeatPump: {
      yes: { required: ['GASDD'] },
    },
    moldRemediationContractorChoice: {
      yes: { required: ['MOLD'] },
    },
  },
  HVAC_Sales: {
    required: ['PICS', 'WS', 'Contract', 'MANUALJ'],
    displaceGasHeatWithHeatPump: {
      yes: { required: ['GASDD'] },
    },
  },
};

const documents = {
  AC: {
    fullName: 'Window AC Unit',
    fileType: ['pdf'],
  },
  AIF: {
    fullName: 'Attic Inspection Form',
    fileType: ['pdf'],
  },
  ATT: {
    fullName: 'ISM Attention',
    fileType: ['pdf'],
  },
  BAS: {
    fullName: 'Bldg Airflow Stand',
    fileType: ['pdf'],
  },
  CST: {
    fullName: 'Combustion Safety Test',
    fileType: ['pdf'],
  },
  Contract: {
    fullName: 'Contract',
    fileType: ['pdf'],
  },
  DEH: {
    fullName: 'Dehumidifier',
    fileType: ['pdf'],
  },
  GASDD: {
    fullName: 'Gas Disclosure Displacement',
    fileType: ['pdf'],
  },
  ISM: {
    fullName: 'ISM Receipt',
    fileType: ['pdf'],
  },
  MANUALJ: {
    fullName: 'Manual J',
    fileType: ['pdf'],
  },
  ODR: {
    fullName: 'Office Data Report',
    fileType: ['pdf'],
  },
  PICS: {
    fullName: 'Pictures',
    fileType: ['pdf'],
  },
  PV: {
    fullName: 'Plan View',
    fileType: ['pdf'],
  },
  HEA: {
    fullName: 'Home Energy Assessment Report',
    fileType: ['pdf'],
  },
  PWB: {
    fullName: 'PreWx Barrier Incentive',
    fileType: ['pdf'],
  },
  PT: {
    fullName: 'Proposal Terms',
    fileType: ['pdf'],
  },
  FRD: {
    fullName: 'Fridge',
    fileType: ['pdf'],
  },
  FRZ: {
    fullName: 'Freezer',
    fileType: ['pdf'],
  },
  VENT: {
    fullName: 'Ventilation Requirements',
    fileType: ['pdf'],
  },
  WD: {
    fullName: 'Wall Disclosure',
    fileType: ['pdf'],
  },
  WAS: {
    fullName: 'Washer Rebate',
    fileType: ['pdf'],
  },
  WAIVERorLTA: {
    fullName: 'WAIVER OR Landlord/Tenant Agreement',
    fileType: ['pdf'],
  },
  'WR-excel': {
    fullName: 'Work Receipt',
    fileType: ['xls', 'xlsm', 'xlsx', 'csv'],
  },
  WS: {
    fullName: 'Work Sheet',
    fileType: ['xls', 'xlsm', 'xlsx', 'csv'],
  },
  WX: {
    fullName: 'Weatherization Proposal',
    fileType: ['pdf'],
  },
  MOLD: {
    fullName: 'Mold',
    fileType: ['pdf'],
  },
};

export { resultingRequiredDocs, documents };
