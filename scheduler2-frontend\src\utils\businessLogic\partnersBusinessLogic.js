const partnerStatusOptions = [
  'Sent for Inspection',
  '1st Attempt to Schedule',
  '2nd Attempt to Schedule',
  'Customer Unresponsive',
  'Inspection Scheduled',
  'Remediation Lost',
  'Inspection Completed',
  'Remediation Scheduled',
  'Barrier Cleared',
];

const issStatusOptions = [
  'Requests Partner Inspection',
  'Sent for Inspection',
  '1st Attempt to Schedule',
  '2nd Attempt to Schedule',
  'Customer Unresponsive',
  'Inspection Scheduled',
  'Remediation Lost',
  'Inspection Completed',
  'Insp Completed - Need Estimate',
  'Contractor Revision Needed',
  'Remediation CAP Approved',
  'Remediation Scheduled',
  'Barrier Cleared',
  'Remediation Invoice Submitted',
];

// TODO: these aren't being used anywhere. Either implement or remove
const columnHeader = [
  'Visit',
  'Partner',
  'Status',
  'Type',
  'Customer',
  'Site ID',
  'Date sent to partner',
  'Inspection Scheduled Date',
  'Notes from homeworks',
  'Notes from partner',
  'Remediation start',
  'Remediation end',
];

const barrierTypeOptions = [
  { key: 'Knob and Tube', value: '008800' },
  { key: 'Mold', value: '008801' },
  { key: 'Vermiculite', value: '008802' },
  { key: 'Asbestos', value: '008803' },
  { key: 'CST Fail (Other)', value: '008816' },
  { key: 'Roofing', value: '008813' },
  { key: 'Structural Limitations', value: '008804' },
  { key: 'Bulk Moisture', value: '008805' },
  { key: 'Failed CST Draft', value: '008806' },
  { key: 'Failed CST High CO', value: '008807' },
  { key: 'Failed CST Oven/Dryer', value: '008808' },
  { key: 'Failed CST Spillage', value: '008809' },
  { key: 'Inaccessible Dirt Crawlspace', value: '008810' },
  { key: 'Kitchen Vent', value: '008811' },
  { key: 'Pest infestation', value: '008815' },
  { key: 'Bath Fan', value: '008814' },
  { key: 'InOperable Heating System', value: '008817' },
  { key: 'Windows', value: '008818' },
  { key: 'Other Barrier', value: '008812' },
];

export { partnerStatusOptions, issStatusOptions, columnHeader, barrierTypeOptions };
