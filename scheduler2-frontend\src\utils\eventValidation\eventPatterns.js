import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';

const create = (params) => {
  const { patternName, startDate, endDate, repeatInterval, eventType, oids } = params;

  const requiredFields = {
    'Pattern name': patternName,
    'Start date': startDate,
    'End date': endDate,
    'Repeat Interval': repeatInterval,
    Type: eventType,
    HESs: oids.length,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  // TODO: multiple day events

  const createPatternData = {
    patternName,
    startDate,
    endDate,
    repeatInterval,
    eventType,
    oids,
  };

  return createPatternData;
};

const update = (params) => {
  const { patternId, patternName, startDate, endDate, repeatInterval, eventType, oids } = params;

  const requiredFields = {
    'Pattern Id': patternId,
    'Pattern name': patternName,
    'Start date': startDate,
    'End date': endDate,
    'Repeat Interval': repeatInterval,
    Type: eventType,
    HESs: oids.length,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  // TODO: multiple day events

  const createPatternData = {
    patternId,
    patternName,
    startDate,
    endDate,
    repeatInterval,
    eventType,
    oids,
  };

  return createPatternData;
};

export default { create, update };
