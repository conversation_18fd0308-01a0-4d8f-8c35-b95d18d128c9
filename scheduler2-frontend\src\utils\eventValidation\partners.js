import validateRequiredParams from '@utils/validateRequiredParams';
import { throwError } from '@utils/EventEmitter';

const createPartnerEvent = (params) => {
  const {
    address,
    oid,
    status,
    sfIds: { accountId, barrierId, dealId },
    type,
    customerName,
    email,
    barrierTypes,
  } = params;

  const requiredFields = {
    'Customer Address': address,
    Partner: oid,
    Status: status,
    'Account Id': accountId,
    'Barrier Id': barrierId,
    'Deal Id': dealId,
    'Job Type': type,
    Customer: customerName,
    'Customer Email': email,
  };

  const selectedBarrierType = barrierTypes?.find(({ eventType }) => {
    return eventType === type;
  });

  if (!selectedBarrierType) return throwError('Barrier Not Created for Selected Job Type');

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  if (![15, 18].includes(barrierId.length))
    return throwError(
      `Invalid Barrier Id entered. Please enter a valid Barrier Id. Current Barrier ID: ${barrierId}`,
    );

  return params;
};

const updatePartnerEvent = (params) => {
  const {
    address,
    oid,
    status,
    sfIds: { accountId, barrierId, dealId },
    type,
    customerName,
    email,
  } = params;

  const requiredFields = {
    'Customer Address': address,
    Partner: oid,
    Status: status,
    'Account Id': accountId,
    'Barrier Id': barrierId,
    'Deal Id': dealId,
    'Job Type': type,
    Customer: customerName,
    'Customer Email': email,
  };

  const missingParams = validateRequiredParams(requiredFields);
  if (missingParams.length)
    return throwError(`Missing required fields. Please fill out ${missingParams.join(', ')}`);
  if (![15, 18].includes(barrierId.length))
    return throwError(
      `Invalid Barrier Id entered. Please enter a valid Barrier Id. Current Barrier ID: ${barrierId}`,
    );

  return params;
};

const partners = { createPartnerEvent, updatePartnerEvent };

export default partners;
