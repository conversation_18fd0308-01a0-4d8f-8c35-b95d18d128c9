import React from 'react';
import styled from 'styled-components';
import Swal from 'sweetalert2/dist/sweetalert2';
import { parseJSXContentForSwalPopup } from '@utils/functions';
import { ScriptText } from '@pages/LeadIntake/LeadIntakeScript/styles';

const Divider = styled.br``;

const validateHeatingFuel = (numUnitsSchedulingToday, heatingFuel) => {
  for (let item = 0; item < Number(numUnitsSchedulingToday); item++) {
    if (heatingFuel?.[item]) {
      return true;
    }
  }
  return false;
};

const validateElectricProvider = (params) => {
  const { numUnitsSchedulingToday, heatingFuel, electricProvider, gasProvider, heaOrHvac } = params;
  if (heaOrHvac === 'HVAC') return true;
  for (let item = 0; item < Number(numUnitsSchedulingToday); item++) {
    const isGasAndElectricProviderIsNotMunicipal =
      heatingFuel?.[item] !== 'Gas' &&
      electricProvider?.[item] &&
      !electricProvider?.some((provider) => provider === 'Municipal');

    if (isGasAndElectricProviderIsNotMunicipal) {
      return true;
    }

    const isGasAndGasProviderIsNotFromAllowedList =
      heatingFuel?.[item] === 'Gas' &&
      gasProvider?.length &&
      ['National Grid', 'Eversource', 'Columbia Gas', 'Other'].some(
        (gp) => gp === gasProvider?.[item],
      ) &&
      electricProvider?.[item];

    if (isGasAndGasProviderIsNotFromAllowedList) {
      return true;
    }
    if (!electricProvider?.[item]) {
      return false;
    }

    Swal.fire({
      icon: 'error',
      title: 'Hold up!',
      html: parseJSXContentForSwalPopup(
        <ScriptText>
          Unfortunately we can only serve National Grid, Eversource, or Columbia Gas customers as
          part of the program. If you would still like to have access to any energy efficiency
          programs available, please reach out to your municipality to learn more.
        </ScriptText>,
      ),
    });
  }
  return false;
};

const validateGasProvider = (params) => {
  const { numUnitsSchedulingToday, heatingFuel, electricProvider, gasProvider, heaOrHvac } = params;
  if (heaOrHvac === 'HVAC') return true;
  for (let item = 0; item < Number(numUnitsSchedulingToday); item++) {
    const isGasProvider = heatingFuel?.[item] === 'Gas' && !gasProvider?.[item] === '';

    const isGasAndGasProviderIsFromNotAllowedList =
      heatingFuel?.[item] === 'Gas' &&
      ['Municipal', 'Berkshire', 'Liberty', 'Blackstone'].some((gp) => gp === gasProvider?.[item]);
    const isGasAndGasProviderBerkshire =
      isGasAndGasProviderIsFromNotAllowedList &&
      gasProvider?.[item] === 'Berkshire' &&
      electricProvider?.[item];

    const isGasAndGasProviderMunicipal =
      gasProvider?.[item] === 'Municipal' && electricProvider?.[item];

    if (isGasProvider) {
      return false;
    }
    if (isGasAndGasProviderMunicipal) {
      Swal.fire({
        icon: 'error',
        title: 'Hold up!',
        html: parseJSXContentForSwalPopup(
          <>
            <ScriptText>
              Unfortunately we can only serve National Grid, Eversource, or Columbia Gas customers
              as part of the program. If you would still like to have access to any energy
              efficiency programs available, please reach out to your municipality to learn more.
            </ScriptText>
            <Divider />
            <Divider />
            <ScriptText bold block>
              Customer Says: But I do have a National Grid gas account for cooking!
            </ScriptText>
            <Divider />
            <Divider />
            <ScriptText>
              Unfortunately, only gas for HEATING is valid for the Mass Save program. There’s
              nothing else we can do at this time. I recommend calling your town to see if they
              offer their own version of the program.
            </ScriptText>
          </>,
        ),
      });
      return false;
    }

    if (isGasAndGasProviderBerkshire) {
      Swal.fire({
        icon: 'error',
        title: 'Hold up!',
        html: parseJSXContentForSwalPopup(
          <ScriptText>
            Unfortunately we are unable to serve customers that receive gas from Berkshire. If you
            would still like to have access to any energy efficiency programs available, please
            contact the Center for EcoTechnology (CET) to learn more. Would you like the number for
            that? (You can reach them directly at **************).
          </ScriptText>,
        ),
      });

      return false;
    }
    if (isGasAndGasProviderIsFromNotAllowedList && electricProvider?.[item]) {
      Swal.fire({
        icon: 'error',
        title: 'Hold up!',
        html: parseJSXContentForSwalPopup(
          <ScriptText>
            Unfortunately we are unable to serve customers that receive gas from
            {gasProvider?.[item]}. If you would still like to have access to any energy efficiency
            programs available, please contact your gas provider to learn more.
          </ScriptText>,
        ),
      });
      return false;
    }
  }
  return true;
};

export { validateHeatingFuel, validateElectricProvider, validateGasProvider };
