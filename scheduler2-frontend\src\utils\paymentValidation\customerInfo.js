import validateRequiredParams from '@utils/validateRequiredParams';

const customerInfo = (params) => {
  const { email, customerName, phoneNumber } = params;
  const requiredFields = {
    Email: email,
    'Customer Name': customerName,
    'Phone Number': phoneNumber,
  };

  const missingParams = validateRequiredParams(requiredFields);

  const isValidPhoneNumber = phonenumber(phoneNumber);
  const isValidEmail = validateEmail(email);

  return missingParams.length === 0 && isValidPhoneNumber && isValidEmail;
};

const validateEmail = (email) => {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/i.test(email) && email !== '<EMAIL>';
};

const phonenumber = (inputtxt) => {
  return /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/.test(inputtxt);
};

export default customerInfo;
