const validateRequiredParams = (requiredParams) => {
  return Object.entries(requiredParams)?.reduce((acc, [key, value]) => {
    if (typeof value === 'function') {
      if (!value()) acc.push(key);
    } else if (
      value == null ||
      value === false ||
      (Array.isArray(value) && value?.every((item) => item == null || item === '')) ||
      (typeof value === 'string' && value?.trim() === '')
    ) {
      acc.push(key);
    }
    return acc;
  }, []);
};

export default validateRequiredParams;
