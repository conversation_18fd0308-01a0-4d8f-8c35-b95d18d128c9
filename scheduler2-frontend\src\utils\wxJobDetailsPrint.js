import { throwError } from '@utils/EventEmitter';

const wxJobDetailsPrint = (event) => {
  const {
    id,
    amount,
    attributes,
    customerName,
    startTime,
    endTime,
    phoneNumber,
    siteId,
    address: { city, displayAddress },
    notes: { officeNotes, fieldNotes },
    sfIds,
  } = event;
  if (!id) {
    return throwError({
      message:
        'This event does not have an ID. This event needs to be scheduled first before you can print job details.',
    });
  }
  const print = window.open('', 'wildebeast', 'width=300,height=300,scrollbars=1,resizable=1');
  if (!print) {
    return throwError({
      message: 'Could not open print window. Please check if your pop-up was blocked.',
    });
  }
  // MISSING WBI - CHECK IF NEEDED
  const html = `
  <html><script>window.onload = function() {window.print();};</script><head></head><body>
    <h1>Job Title:</h1>
    <h3>${city} (${customerName})</h3>
    <h3>${amount}</h3>
    <h3>${attributes.join(', ')}</h3>

    <h1>Job Duration:</h1>
    <h3>${startTime} to ${endTime}</h3>
    
    <h1>Job Location:</h1>
    <h3>${displayAddress}</h3>
    ${customerName &&
      `<h1>Customer Name:</h1>
      <h3>${customerName}</h3>      
      `}
    ${phoneNumber &&
      `<h1>Customer Phone Number:</h1>
      <h3>${phoneNumber}</h3>      
      `}
    <h1>Site ID:</h1>
    <h3>${siteId}</h3>

    <h1>Operation ID:</h1>
    <h3>${sfIds?.operationsId || ''}</h3>

    <h1> Internal Job Notes:</h1>
    <h3>${officeNotes || 'none'}</h3>

    <h1>Job Notes:</h1>
    <h3>${fieldNotes || 'none'}</h3>
  </body></html>
  `;

  print.document.open();
  print.document.write(html);
  print.document.close();
  return true;
};

export default wxJobDetailsPrint;
