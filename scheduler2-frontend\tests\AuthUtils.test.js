import { hasRole } from '@utils/AuthUtils';

const userRoles = {
  roles: [
    { role: 'Super User', roleId: 1, department: 'HVAC-Install', departmentId: 3 },
    { role: 'Super User', roleId: 1, department: 'Insulation', departmentId: 6 },
    { role: 'Super User', roleId: 1, department: 'Software', departmentId: 5 },
    { role: 'Manager', roleId: 2, department: 'Payroll', departmentId: 7 },
    { role: 'Scheduler', roleId: 3, department: 'HES', departmentId: 1 },
  ],
  company: 'HWE',
};

describe('Utils | AuthUtils', () => {
  test('hasRole | Super User', () => {
    expect(hasRole('Super User', null, null, userRoles)).toBe(true);
  });

  test('hasRole | External Scheduler', () => {
    expect(hasRole('External Scheduler', null, null, userRoles)).toBe(false);
  });
});
