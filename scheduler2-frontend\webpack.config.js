/* eslint-disable no-unused-vars */
const webpack = require('webpack');
const path = require('path');
const { WebpackManifestPlugin } = require('webpack-manifest-plugin');

module.exports = (env, options) => {
  const isDevMode = options.mode === 'development';
  const config = {
    entry: path.resolve(__dirname, 'src/index.js'),
    output: {
      path: path.join(__dirname, 'dist'),
      filename: 'bundle.[contenthash].js',
      clean: true,
    },
    resolve: {
      alias: {
        '@assets': path.resolve(__dirname, 'src/assets'),
        '@components': path.resolve(__dirname, 'src/components'),
        '@config': path.resolve(__dirname, 'src/config'),
        '@contexts': path.resolve(__dirname, 'src/contexts'),
        '@utils': path.resolve(__dirname, 'src/utils'),
        '@style': path.resolve(__dirname, 'src/style'),
        '@recoil': path.resolve(__dirname, 'src/recoil'),
        '@hooks': path.resolve(__dirname, 'src/hooks'),
        '@pages': path.resolve(__dirname, 'src/pages'),
      },
    },
    module: {
      rules: [
        {
          test: /\.js$/,
          include: path.resolve(__dirname, 'src'),
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react'],
            plugins: [
              '@babel/plugin-proposal-class-properties',
              '@babel/transform-runtime',
              'babel-plugin-styled-components',
            ],
          },
        },
        {
          test: /\.s[ac]ss$|\.css$/i,
          use: [
            // Creates `style` nodes from JS strings
            'style-loader',
            // Translates CSS into CommonJS
            'css-loader',
            // Compiles Sass to CSS
            // 'sass-loader',
          ],
        },
        {
          test: /\.svg$/,
          loader: 'svg-url-loader',
        },
        {
          test: /\.(gif|jpg|png)$/,
          loader: 'file-loader',
        },
      ],
    },
    devServer: {
      contentBase: path.resolve(__dirname, 'dist'),
      proxy: {
        '**': 'http://localhost:8084/',
      },
    },
    plugins: [new WebpackManifestPlugin()],
    devtool: 'eval-cheap-source-map',
  };
  return config;
};
